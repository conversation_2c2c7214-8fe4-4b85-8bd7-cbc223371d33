package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.service.IShipmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ShipmentOperationInDB {

    private final IShipmentService shipmentService;

    public Shipment createShipment() {
        return shipmentService.saveShipment(Fakers.createShipment());
    }

    public Shipment getShipmentById(Long shipmentId) {
        return shipmentService.findShipmentById(shipmentId);
    }
}
