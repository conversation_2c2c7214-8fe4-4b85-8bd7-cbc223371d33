package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.service.ITripService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TripOperationInDB {

    private final ITripService tripService;

    public Trip createTrip() {
        return tripService.saveTrip(Fakers.createTrip());
    }

    public Trip getTripById(Long tripId) {
        return tripService.findTripById(tripId);
    }
}
