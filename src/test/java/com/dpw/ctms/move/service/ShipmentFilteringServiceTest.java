package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.ShipmentMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShipmentFilteringServiceTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private ShipmentMapper shipmentMapper;

    @InjectMocks
    private ShipmentFilteringService shipmentFilteringService;

    @Captor
    private ArgumentCaptor<Pageable> pageableCaptor;

    private ShipmentListingRequest shipmentListingRequest;
    private List<Shipment> shipmentEntities;
    private List<ShipmentListingResponse> shipmentResponses;

    @BeforeEach
    void setUp() {
        // Setup request using utility
        ShipmentListingRequest.Filter filter = createShipmentIdsFilter("SHIPMENT001", "SHIPMENT002");
        filter.setShipmentStatuses(Arrays.asList(ShipmentStatus.ALLOCATED.name(), "ASSIGNED"));

        shipmentListingRequest = createCustomShipmentListingRequest(
                createDefaultPagination(),
                createDefaultSort(),
                filter
        );

        // Setup shipment entities
        shipmentEntities = Arrays.asList(
                createShipmentEntity("SHIPMENT001", ShipmentStatus.ALLOCATED),
                createShipmentEntity("SHIPMENT002", ShipmentStatus.ASSIGNED)
        );

        // Setup shipment responses
        shipmentResponses = Arrays.asList(
                createShipmentResponse("SHIPMENT001", ShipmentStatus.ALLOCATED),
                createShipmentResponse("SHIPMENT002", ShipmentStatus.ASSIGNED)
        );
    }

    @Test
    void filterShipments_WithValidRequest_ShouldReturnFilteredShipments() {
        // Arrange
        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipmentEntities.get(0))).thenReturn(shipmentResponses.get(0));
        when(shipmentMapper.toResponse(shipmentEntities.get(1))).thenReturn(shipmentResponses.get(1));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(shipmentListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        assertEquals("SHIPMENT001", response.getData().get(0).getCode());
        assertEquals("SHIPMENT002", response.getData().get(1).getCode());
        verify(shipmentRepository).findAll(any(Specification.class), pageableCaptor.capture());

        // Verify pagination and sorting
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber());
        assertEquals(10, pageable.getPageSize());
        assertEquals("createdAt: DESC", pageable.getSort().toString());
    }

    @Test
    void filterShipments_WithNullFilter_ShouldReturnAllShipments() {
        // Arrange
        ShipmentListingRequest requestWithNullFilter = ShipmentListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(null)
                .build();

        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipmentEntities.get(0))).thenReturn(shipmentResponses.get(0));
        when(shipmentMapper.toResponse(shipmentEntities.get(1))).thenReturn(shipmentResponses.get(1));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithNullFilter);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
    }

    @Test
    void filterShipments_WithNullPagination_ShouldUseDefaultPagination() {
        // Arrange
        ShipmentListingRequest requestWithNullPagination = ShipmentListingRequest.builder()
                .pagination(null)
                .sort(new Sort("createdAt", "DESC"))
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipmentEntities.get(0))).thenReturn(shipmentResponses.get(0));
        when(shipmentMapper.toResponse(shipmentEntities.get(1))).thenReturn(shipmentResponses.get(1));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithNullPagination);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(shipmentRepository).findAll(any(Specification.class), pageableCaptor.capture());

        // Verify default pagination
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber()); // Default page number
        assertEquals(20, pageable.getPageSize()); // Default page size
    }

    @Test
    void filterShipments_WithNullSort_ShouldUseDefaultSort() {
        // Arrange
        ShipmentListingRequest requestWithNullSort = ShipmentListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(null)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        Page<Shipment> shipmentPage = new PageImpl<>(shipmentEntities, Pageable.ofSize(10), 2);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipmentEntities.get(0))).thenReturn(shipmentResponses.get(0));
        when(shipmentMapper.toResponse(shipmentEntities.get(1))).thenReturn(shipmentResponses.get(1));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithNullSort);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(shipmentRepository).findAll(any(Specification.class), pageableCaptor.capture());

        // Verify default sort
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("createdAt: DESC", pageable.getSort().toString()); // Default sort
    }

    @Test
    void filterShipments_WithEmptyResult_ShouldReturnEmptyList() {
        // Arrange
        Page<Shipment> emptyPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(shipmentListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertEquals(0, response.getData().size());
    }

    @Test
    void filterShipments_WithTripIds_ShouldFilterByTripIds() {
        // Arrange
        ShipmentListingRequest.Filter filterWithTripIds = new ShipmentListingRequest.Filter();
        filterWithTripIds.setTripIds(Arrays.asList("TRIP001"));

        ShipmentListingRequest requestWithTripIds = ShipmentListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithTripIds)
                .build();

        Shipment shipment = createComplexShipmentEntity();
        shipment.getTrip().setCode("TRIP001");
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIPMENT001", ShipmentStatus.ALLOCATED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithTripIds);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterShipments_WithTripStatuses_ShouldFilterByTripStatuses() {
        // Arrange
        ShipmentListingRequest.Filter filterWithTripStatuses = new ShipmentListingRequest.Filter();
        filterWithTripStatuses.setTripStatuses(Arrays.asList("CREATED"));

        ShipmentListingRequest requestWithTripStatuses = ShipmentListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithTripStatuses)
                .build();

        Shipment shipment = createComplexShipmentEntity();
        shipment.getTrip().setStatus(TripStatus.CREATED);
        Page<Shipment> shipmentPage = new PageImpl<>(Collections.singletonList(shipment), Pageable.ofSize(10), 1);

        when(shipmentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(shipmentPage);
        when(shipmentMapper.toResponse(shipment)).thenReturn(createShipmentResponse("SHIPMENT001", ShipmentStatus.ALLOCATED));

        // Act
        ListResponse<ShipmentListingResponse> response = shipmentFilteringService.filterShipments(requestWithTripStatuses);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    // Helper methods
    private Pagination createDefaultPagination() {
        return new Pagination(0, 10);
    }

    private Sort createDefaultSort() {
        return new Sort(ShipmentFieldConstants.SortFields.API_CREATED_AT, "DESC");
    }

    private ShipmentListingRequest.Filter createShipmentIdsFilter(String... shipmentIds) {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList(shipmentIds));
        return filter;
    }

    private ShipmentListingRequest createCustomShipmentListingRequest(Pagination pagination, Sort sort, ShipmentListingRequest.Filter filter) {
        return ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();
    }

    private Shipment createShipmentEntity(String code, ShipmentStatus status) {
        Shipment shipment = new Shipment();
        shipment.setCode(code);
        shipment.setStatus(status);
        shipment.setExternalConsignmentId("CONS-" + code);
        shipment.setExternalCustomerOrderId("CO-" + code);
        shipment.setExpectedPickupAt(System.currentTimeMillis());
        shipment.setExpectedDeliveryAt(System.currentTimeMillis() + 7200000L);
        shipment.setActualPickupAt(System.currentTimeMillis() + 1800000L);
        shipment.setActualDeliveryAt(System.currentTimeMillis() + 9000000L);
        shipment.setWeight(BigDecimal.valueOf(100.0));
        shipment.setVolume(BigDecimal.valueOf(50.0));

        // Create trip
        Trip trip = new Trip();
        trip.setCode("TRIP-" + code);
        trip.setStatus(TripStatus.CREATED);
        trip.setExternalOriginLocationCode("ORIGIN-" + code);
        trip.setExternalDestinationLocationCode("DEST-" + code);
        shipment.setTrip(trip);

        // Create transport order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO-" + code);
        transportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        transportOrder.setAssigneeIdentifier("VENDOR-" + code);
        transportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        shipment.setTransportOrder(transportOrder);

        // Create origin stop
        Stop originStop = new Stop();
        originStop.setExternalLocationCode("ORIGIN-" + code);
        shipment.setOriginStop(originStop);

        // Create destination stop
        Stop destinationStop = new Stop();
        destinationStop.setExternalLocationCode("DEST-" + code);
        shipment.setDestinationStop(destinationStop);

        return shipment;
    }

    private Shipment createComplexShipmentEntity() {
        Shipment shipment = createShipmentEntity("SHIPMENT001", ShipmentStatus.ALLOCATED);

        // Add vehicle resource to trip
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE001");
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG001");
        vehicleResource.setTrip(shipment.getTrip());
        shipment.getTrip().setVehicleResource(vehicleResource);

        // Add trailer resources to trip
        Set<TrailerResource> trailerResources = new HashSet<>();
        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId("TRAILER001");
        trailerResource.setTrip(shipment.getTrip());
        trailerResources.add(trailerResource);
        shipment.getTrip().setTrailerResources(trailerResources);

        // Add vehicle operator resources to trip
        Set<VehicleOperatorResource> operatorResources = new HashSet<>();
        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId("DRIVER001");
        operatorResource.setTrip(shipment.getTrip());
        operatorResources.add(operatorResource);
        shipment.getTrip().setVehicleOperatorResources(operatorResources);

        return shipment;
    }

    private ShipmentListingResponse createShipmentResponse(String code, ShipmentStatus status) {
        EnumLabelValueResponse statusInfo = new EnumLabelValueResponse(status.getDisplayName(), status.name());
        return ShipmentListingResponse.builder()
                .code(code)
                .status(statusInfo)
                .tripCode("TRIP-" + code)
                .transportOrderCode("TO-" + code)
                .customerOrderId("CO-" + code)
                .consignmentId("CONS-" + code)
                .build();
    }
}
