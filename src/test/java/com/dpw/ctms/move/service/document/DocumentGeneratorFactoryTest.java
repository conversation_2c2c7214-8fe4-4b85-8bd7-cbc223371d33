package com.dpw.ctms.move.service.document;

import com.dpw.ctms.move.enums.DocumentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DocumentGeneratorFactoryTest {

    @Mock
    private DocumentGenerator<?> bolDocumentGenerator;

    @Mock
    private DocumentGenerator<?> manifestDocumentGenerator;

    private DocumentGeneratorFactory documentGeneratorFactory;

    @BeforeEach
    void setUp() {
        // Setup moved to individual tests to avoid unnecessary stubbing warnings
    }

    @Test
    void shouldInitializeWithSingleGenerator() {
        // Given
        when(bolDocumentGenerator.getDocumentType()).thenReturn(DocumentType.BOL);
        List<DocumentGenerator<?>> generators = Collections.singletonList(bolDocumentGenerator);

        // When
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // Then
        DocumentGenerator<?> result = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        assertNotNull(result);
        assertSame(bolDocumentGenerator, result);
    }

    @Test
    void shouldInitializeWithMultipleGenerators() {
        // Given
        when(bolDocumentGenerator.getDocumentType()).thenReturn(DocumentType.BOL);
        when(manifestDocumentGenerator.getDocumentType()).thenReturn(DocumentType.MANIFEST);
        List<DocumentGenerator<?>> generators = Arrays.asList(bolDocumentGenerator, manifestDocumentGenerator);

        // When
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // Then
        DocumentGenerator<?> bolResult = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        DocumentGenerator<?> manifestResult = documentGeneratorFactory.getGenerator(DocumentType.MANIFEST);
        
        assertNotNull(bolResult);
        assertNotNull(manifestResult);
        assertSame(bolDocumentGenerator, bolResult);
        assertSame(manifestDocumentGenerator, manifestResult);
    }

    @Test
    void shouldInitializeWithEmptyGeneratorList() {
        // Given
        List<DocumentGenerator<?>> generators = Collections.emptyList();

        // When
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // Then - factory should be created but no generators available
        assertNotNull(documentGeneratorFactory);
    }

    @Test
    void shouldThrowIllegalArgumentExceptionWhenGeneratorNotFound() {
        // Given
        when(bolDocumentGenerator.getDocumentType()).thenReturn(DocumentType.BOL);
        List<DocumentGenerator<?>> generators = Collections.singletonList(bolDocumentGenerator);
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> documentGeneratorFactory.getGenerator(DocumentType.MANIFEST));
        
        assertEquals("No DocumentGenerator found for type: MANIFEST", exception.getMessage());
    }

    @Test
    void shouldThrowIllegalArgumentExceptionWhenNoGeneratorsRegistered() {
        // Given
        List<DocumentGenerator<?>> generators = Collections.emptyList();
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> documentGeneratorFactory.getGenerator(DocumentType.BOL));
        
        assertEquals("No DocumentGenerator found for type: BOL", exception.getMessage());
    }

    @Test
    void shouldHandleNullDocumentType() {
        // Given
        when(bolDocumentGenerator.getDocumentType()).thenReturn(DocumentType.BOL);
        List<DocumentGenerator<?>> generators = Collections.singletonList(bolDocumentGenerator);
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> documentGeneratorFactory.getGenerator(null));
        
        assertEquals("No DocumentGenerator found for type: null", exception.getMessage());
    }

    @Test
    void shouldReturnSameInstanceOnMultipleCalls() {
        // Given
        when(bolDocumentGenerator.getDocumentType()).thenReturn(DocumentType.BOL);
        List<DocumentGenerator<?>> generators = Collections.singletonList(bolDocumentGenerator);
        documentGeneratorFactory = new DocumentGeneratorFactory(generators);

        // When
        DocumentGenerator<?> first = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        DocumentGenerator<?> second = documentGeneratorFactory.getGenerator(DocumentType.BOL);

        // Then
        assertSame(first, second);
    }

}