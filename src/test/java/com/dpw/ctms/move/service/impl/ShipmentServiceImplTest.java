package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ShipmentServiceImplTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @InjectMocks
    private ShipmentServiceImpl shipmentService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findShipmentById_found() {
        Shipment shipment = new Shipment();
        shipment.setId(1L);
        Mockito.when(shipmentRepository.findById(1L)).thenReturn(Optional.of(shipment));
        Shipment result = shipmentService.findShipmentById(1L);
        assertThat(result).isEqualTo(shipment);
    }

    @Test
    void findShipmentById_notFound() {
        Mockito.when(shipmentRepository.findById(2L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> shipmentService.findShipmentById(2L))
                .isInstanceOf(TMSException.class)
                .hasMessageContaining("NOT_FOUND");
    }

    @Test
    void findShipmentByCode_found() {
        Shipment shipment = new Shipment();
        shipment.setCode("CODE123");
        Mockito.when(shipmentRepository.findByCode("CODE123")).thenReturn(Optional.of(shipment));
        Shipment result = shipmentService.findShipmentByCode("CODE123");
        assertThat(result).isEqualTo(shipment);
    }

    @Test
    void findShipmentByCode_notFound() {
        Mockito.when(shipmentRepository.findByCode("NOT_FOUND")).thenReturn(Optional.empty());
        assertThatThrownBy(() -> shipmentService.findShipmentByCode("NOT_FOUND"))
                .isInstanceOf(TMSException.class)
                .hasMessageContaining("NOT_FOUND");
    }
}
