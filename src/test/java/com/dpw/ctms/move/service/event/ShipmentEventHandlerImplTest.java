package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.dto.EventRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.request.message.ShipmentStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.impl.ShipmentEventHandlerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ShipmentEventHandlerImplTest {

    @Mock
    private IEventProcessorService<ShipmentStatusUpdateMessage> eventProcessorService;

    @Mock
    private IEntityEventManager entityEventManager;

    @InjectMocks
    private ShipmentEventHandlerImpl shipmentEventHandler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(shipmentEventHandler, "shipmentStatusUpdateTopicName", "test-topic");
    }

    @Test
    void register_ShouldRegisterWithEntityEventManager() {
        shipmentEventHandler.register();
        verify(entityEventManager).register(eq(StateMachineEntityType.SHIPMENT.name()), eq(shipmentEventHandler));
    }

    @Test
    void updateStatusEvent_ShouldProcessEventSuccessfully() {
        EventRequestDTO<Shipment> eventRequest = Fakers.createDefaultShipmentEventRequestDTO();
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("SHIPMENT_STATUS_UPDATE"), any())).thenReturn(mockResponse);
        
        IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("SHIPMENT_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithCompleteData_ShouldIncludeAllFields() {
        EventRequestDTO<Shipment> eventRequest = Fakers.createShipmentEventRequestDTO(
                "SHP001", ShipmentStatus.ALLOCATED, ShipmentStatus.CANCELLED);
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("SHIPMENT_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("SHIPMENT_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WhenExceptionOccurs_ShouldReturnEmptyResponse() {
        EventRequestDTO<Shipment> eventRequest = Fakers.createDefaultShipmentEventRequestDTO();
        doThrow(new RuntimeException("Test exception")).when(eventProcessorService).processRequest(any(), any());
        
        IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertFalse(result.isSuccess()); // Empty builder creates response with success = false
        verify(eventProcessorService).processRequest(any(), any());
    }

    @Test
    void integrationTest_MultipleShipments_ShouldProcessAllCorrectly() {
        shipmentEventHandler.register();
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("SHIPMENT_STATUS_UPDATE"), any())).thenReturn(mockResponse);
        
        for (int i = 1; i <= 3; i++) {
            String shipmentCode = "SHP" + String.format("%03d", i);
            EventRequestDTO<Shipment> eventRequest = Fakers.createShipmentEventRequestDTO(
                    shipmentCode, ShipmentStatus.ASSIGNED, ShipmentStatus.ALLOCATED);
            IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);
            
            assertNotNull(result);
            assertTrue(result.isSuccess());
        }

        verify(eventProcessorService, times(3)).processRequest(eq("SHIPMENT_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithEventTypeAndComments_ShouldIncludeInMessage() {
        EventRequestDTO<Shipment> eventRequest = Fakers.createShipmentEventRequestDTO(
                "SHP001", ShipmentStatus.ALLOCATED, ShipmentStatus.CANCELLED);
        eventRequest.setEventType("MANUAL_UPDATE");
        eventRequest.setComments("Manual status update by user");
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("SHIPMENT_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("SHIPMENT_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithActualTimestamps_ShouldIncludeInMessage() {
        EventRequestDTO<Shipment> eventRequest = Fakers.createShipmentEventRequestDTO(
                "SHP001", ShipmentStatus.IN_TRANSIT, ShipmentStatus.DELIVERED);
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("SHIPMENT_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = shipmentEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("SHIPMENT_STATUS_UPDATE"), any());
    }

}