package com.dpw.ctms.move.service.document.generator;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.dto.document.BOLDocumentDataDTO;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.NotFoundException;
import com.dpw.tmsutils.exception.TMSException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@DisplayName("BOL Document Generator Tests")
class BOLDocumentGeneratorTest extends BaseTest {

    @Autowired
    private BOLDocumentGenerator bolDocumentGenerator;

    @MockBean
    private ITripDataService tripDataService;

    @MockBean
    private OmsServiceAdapter omsServiceAdapter;

    @MockBean
    private ResourceServiceAdapter resourceServiceAdapter;

    @MockBean
    private ConfigService configService;

    private Trip testTrip;
    private OmsListResponse<ConsignmentRecord> consignmentResponse;
    private ListResponse<FacilityRecord> facilityResponse;
    private ListResponse<UomRecord> uomResponse;

    @BeforeEach
    void setUp() {
        testTrip = Fakers.createBOLTestTrip();
        consignmentResponse = Fakers.createOmsConsignmentResponse();
        facilityResponse = Fakers.createFacilityResponse();
        uomResponse = Fakers.createUomResponse();
    }

    @Test
    @DisplayName("Should return correct document type")
    void shouldReturnCorrectDocumentType() {
        // When
        DocumentType result = bolDocumentGenerator.getDocumentType();

        // Then
        assertEquals(DocumentType.BOL, result);
    }

    @Test
    @DisplayName("Should generate context successfully with valid trip")
    void shouldGenerateContextSuccessfully() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, vendor);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        assertNotNull(result);
        assertEquals(vendor, result.getVendor());
        assertEquals(DocumentType.BOL, result.getDocumentType());
        assertNotNull(result.getDocumentData());

        BOLDocumentDataDTO documentData = result.getDocumentData();
        assertNotNull(documentData.getTripDetails());
        assertNotNull(documentData.getConsignmentDetailsMap());
        assertNotNull(documentData.getFacilityDetailsMap());
        assertFalse(documentData.getExternalConsignmentIds().isEmpty());
        assertFalse(documentData.getExternalFacilityCodes().isEmpty());

        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsServiceAdapter).getConsignmentList(any(), any());
        verify(resourceServiceAdapter).getFacilityList(any(),any());
        verify(resourceServiceAdapter).getUomList(any(), any());
    }

    @Test
    @DisplayName("Should throw exception when trip not found")
    void shouldThrowExceptionWhenTripNotFound() {
        // Given
        String tripCode = "INVALID_TRIP";
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Vendor.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertTrue(exception.getCause() instanceof NotFoundException);
    }

    @Test
    @DisplayName("Should propagate OMS service exceptions")
    void shouldPropagateOmsServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException omsException = new GenericException("OMS Error", "OMS_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenThrow(omsException);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Vendor.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(omsException, exception.getCause());
    }

    @Test
    @DisplayName("Should propagate Resource service exceptions")
    void shouldPropagateResourceServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException resourceException = new GenericException("Resource Error", "RES_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenThrow(resourceException);
        when(resourceServiceAdapter.getUomList(any(),  any())).thenReturn(uomResponse);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Vendor.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(resourceException, exception.getCause());
    }

    @Test
    @DisplayName("Should handle empty consignment response")
    void shouldHandleEmptyConsignmentResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        OmsListResponse<ConsignmentRecord> emptyResponse = Fakers.createEmptyOmsResponse();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(emptyResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Vendor.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getConsignmentDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should handle empty facility response")
    void shouldHandleEmptyFacilityResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        ListResponse<FacilityRecord> emptyFacilityResponse = Fakers.createEmptyFacilityResponse();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(emptyFacilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Vendor.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getFacilityDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should generate JSON successfully with valid JOLT configuration")
    void shouldGenerateJsonSuccessfully() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;
        
        // Mock all the dependencies for generateContext
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // Mock document configuration - simple transformation that copies input to output
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor))
                .thenReturn(documentConfig);

        // When
        String result = bolDocumentGenerator.generateJson(tripCode, vendor);

        // Then
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
        assertTrue(result.contains("tripDetails") || result.contains("{}") || result.length() > 2);
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, vendor);
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is null")
    void shouldThrowExceptionWhenJoltConfigIsNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor))
                .thenReturn(null);

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> bolDocumentGenerator.generateJson(tripCode, vendor));
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is empty")
    void shouldThrowExceptionWhenJoltConfigIsEmpty() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);
        
        // Empty JOLT configuration array
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode emptyJoltConfig = objectMapper.createArrayNode();
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", emptyJoltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor))
                .thenReturn(documentConfig);

        // When & Then
        TMSException exception = assertThrows(TMSException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, vendor));
    }

    @Test
    @DisplayName("Should propagate exception from generateContext")
    void shouldPropagateExceptionFromGenerateContext() {
        // Given
        String tripCode = "INVALID_TRIP";
        Vendor vendor = Vendor.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, vendor));
        
        assertTrue(exception.getMessage().contains("Trip not found"));
    }

    @Test
    @DisplayName("Should handle JOLT transformation returning null")
    void shouldHandleJoltTransformationReturningNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // JOLT configuration that might return null (invalid transformation)
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "remove")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor))
                .thenReturn(documentConfig);

        // When
        String result = bolDocumentGenerator.generateJson(tripCode, vendor);

        // Then - Should return empty object as JSON when transformation returns null
        assertNotNull(result);
        assertTrue(result.equals("{}") || result.equals("{ }") || result.trim().equals("{}"));
    }

    @Test
    @DisplayName("Should generate different JSON for different vendors")
    void shouldGenerateDifferentJsonForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor1 = Vendor.CFR;
        Vendor vendor2 = Vendor.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Different JOLT configs for different vendors
        JsonNode joltConfig1 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "trip")));
        
        JsonNode joltConfig2 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "tripInfo")));
        
        JsonNode documentConfig1 = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig1);
        
        JsonNode documentConfig2 = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig2);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor1))
                .thenReturn(documentConfig1);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, vendor2))
                .thenReturn(documentConfig2);

        // When
        String result1 = bolDocumentGenerator.generateJson(tripCode, vendor1);
        String result2 = bolDocumentGenerator.generateJson(tripCode, vendor2);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        // Results should be different due to different JOLT transformations
        // (Though both might be valid JSON, the structure will differ)
        assertFalse(result1.trim().isEmpty());
        assertFalse(result2.trim().isEmpty());
        
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, vendor1);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, vendor2);
    }

    @Test
    @DisplayName("Should retrieve correct templateId for different vendors")
    void shouldRetrieveCorrectTemplateIdForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor cfrVendor = Vendor.CFR;
        Vendor ihsVendor = Vendor.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(), any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(), any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),any()))
                .thenReturn(uomResponse);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode cfrConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        JsonNode ihsConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, cfrVendor))
                .thenReturn(cfrConfig);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, ihsVendor))
                .thenReturn(ihsConfig);

        // When
        bolDocumentGenerator.generateJson(tripCode, cfrVendor);
        bolDocumentGenerator.generateJson(tripCode, ihsVendor);

        // Then - Verify different template IDs are retrieved for different vendors
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, cfrVendor);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, ihsVendor);
    }

    @Test
    @DisplayName("Should handle InterruptedException correctly")
    void shouldHandleInterruptedException() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Vendor vendor = Vendor.CFR;
        
        // Mock to throw InterruptedException during execution
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenAnswer(invocation -> {
                    Thread.currentThread().interrupt();
                    throw new InterruptedException("Thread was interrupted");
                });

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, vendor));
    }
}