package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.service.impl.TaskServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TaskServiceImplTest {
    @Mock
    private TaskInstanceRegistrationRequestMapper mapper;
    @Mock
    private TaskRepository taskRepository;
    @Mock
    private TaskServiceAdapter adapter;
    @InjectMocks
    private TaskServiceImpl service;
    @Mock
    private TransportOrderRepository transportOrderRepository;

    @Test
    void testInstantiateTasks() {
        Task task = new Task();
        task.setCode("code1");
        TaskInstanceRegistrationRequest req = mock(TaskInstanceRegistrationRequest.class);
        when(mapper.toTaskInstanceRegisterRequest(anyList())).thenReturn(req);
        TaskInstanceRegistrationResponse resp = new TaskInstanceRegistrationResponse();
        resp.setExtTaskTransactionCode("code1");
        resp.setTaskRegistrationCode("reg1");
        when(adapter.registerTaskInstance(req)).thenReturn(List.of(resp));
        service.instantiateTasks(List.of(task));
        assertEquals("reg1", task.getExternalTaskRegistrationCode());
        verify(taskRepository).saveAll(anyList());
    }
}
