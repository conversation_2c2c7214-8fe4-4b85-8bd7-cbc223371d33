package com.dpw.ctms.move.service;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.helper.ShipmentOperationInDB;
import com.dpw.ctms.move.helper.TaskOperationInDB;
import com.dpw.ctms.move.helper.TripOperationInDB;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;

@AutoConfigureMockMvc
@ExtendWith(MockitoExtension.class)
public class StateMachineTest extends BaseTest {

    @MockBean
    private StateMachineConfigReader stateMachineConfigReader;

    @Autowired
    private TaskOperationInDB taskOperationInDB;

    @Autowired
    private StateMachineServiceRegistry stateMachineServiceRegistry;

    @Autowired
    private ShipmentOperationInDB shipmentOperationInDB;

    @Autowired
    private TripOperationInDB tripOperationInDB;

    @Test
    public void exception_thrown_when_is_initial_is_not_set_for_any_states() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMapWithoutInitialState("CFR"));
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        TMSException exception = Assertions.assertThrows(TMSException.class, () -> {
            stateMachineService.handleEvent("CFR", "TASK_COMPLETED", task.getId());
        });
        Assertions.assertTrue(exception.getErrorMessage().contains("Exception occurred while creating state machine"));
    }

    @Test
    public void change_task_status_from_CREATED_to_COMPLETED() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMap("CFR"));
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        stateMachineService.handleEvent("CFR", "TASK_COMPLETED", task.getId());
        Assertions.assertEquals(TaskStatus.COMPLETED, taskOperationInDB.getTaskById(task.getId()).getStatus());
    }


    @Test
    public void change_shipment_status_from_ASSIGNED_to_ALLOCATED() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMap("CFR"));
        Shipment shipment = shipmentOperationInDB.createShipment();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.SHIPMENT);
        stateMachineService.handleEvent("CFR",
                ShipmentLifecycleEvent.RESOURCE_ALLOCATED.name(), shipment.getId());
        Assertions.assertEquals(ShipmentStatus.ALLOCATED,
                shipmentOperationInDB.getShipmentById(shipment.getId()).getStatus());
    }

    @Test
    public void change_trip_status_from_CREATED_to_IN_PROGRESS() {
        Mockito.when(stateMachineConfigReader.readStateMachineConfig())
                .thenReturn(Fakers.createStateMachineTenantConfigMap("CFR"));
        Trip trip = tripOperationInDB.createTrip();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TRIP);
        stateMachineService.handleEvent("CFR",
                TripLifecycleEvent.START_TRIP.name(), trip.getId());
        Assertions.assertEquals(TripStatus.IN_PROGRESS,
                tripOperationInDB.getTripById(trip.getId()).getStatus());
    }
}