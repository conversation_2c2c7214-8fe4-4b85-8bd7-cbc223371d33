package com.dpw.ctms.move.service.event;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.EventRequestDTO;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.request.message.TripStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.impl.TripEventHandlerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TripEventHandlerImplTest {

    @Mock
    private IEventProcessorService<TripStatusUpdateMessage> eventProcessorService;

    @Mock
    private IEntityEventManager entityEventManager;

    @InjectMocks
    private TripEventHandlerImpl tripEventHandler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(tripEventHandler, "tripStatusUpdateTopicName", "test-trip-topic");
    }

    @Test
    void register_ShouldRegisterWithEntityEventManager() {
        tripEventHandler.register();
        verify(entityEventManager).register(eq(StateMachineEntityType.TRIP.name()), eq(tripEventHandler));
    }

    @Test
    void updateStatusEvent_ShouldProcessEventSuccessfully() {
        EventRequestDTO<Trip> eventRequest = Fakers.createDefaultTripEventRequestDTO();
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TRIP_STATUS_UPDATE"), any())).thenReturn(mockResponse);
        
        IntegratorMessageResponse result = tripEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TRIP_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithCompleteData_ShouldIncludeAllFields() {
        EventRequestDTO<Trip> eventRequest = Fakers.createTripEventRequestDTO("TRIP001",
                TripStatus.CLOSED, TripStatus.COMPLETED);
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TRIP_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = tripEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TRIP_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WhenExceptionOccurs_ShouldReturnEmptyResponse() {
        EventRequestDTO<Trip> eventRequest = Fakers.createDefaultTripEventRequestDTO();
        doThrow(new RuntimeException("Test exception")).when(eventProcessorService).processRequest(any(), any());
        
        IntegratorMessageResponse result = tripEventHandler.updateStatusEvent(eventRequest);
        
        assertNotNull(result);
        assertFalse(result.isSuccess()); // Empty builder creates response with success = false
        verify(eventProcessorService).processRequest(any(), any());
    }

    @Test
    void updateStatusEvent_WithEventTypeAndComments_ShouldIncludeInMessage() {
        EventRequestDTO<Trip> eventRequest = Fakers.createTripEventRequestDTO("TRIP001",
                TripStatus.CLOSED, TripStatus.COMPLETED);
        eventRequest.setEventType("MANUAL_UPDATE");
        eventRequest.setComments("Manual trip completion");
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TRIP_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = tripEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TRIP_STATUS_UPDATE"), any());
    }

    @Test
    void updateStatusEvent_WithActualTimestamps_ShouldIncludeInMessage() {
        EventRequestDTO<Trip> eventRequest = Fakers.createTripEventRequestDTO("TRIP001",
                TripStatus.CLOSED, TripStatus.CANCELLED);
        
        IntegratorMessageResponse mockResponse = IntegratorMessageResponse.builder().success(true).build();
        when(eventProcessorService.processRequest(eq("TRIP_STATUS_UPDATE"), any())).thenReturn(mockResponse);

        IntegratorMessageResponse result = tripEventHandler.updateStatusEvent(eventRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(eventProcessorService).processRequest(eq("TRIP_STATUS_UPDATE"), any());
    }
}