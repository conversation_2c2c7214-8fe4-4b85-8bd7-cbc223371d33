package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ShipmentDetailsDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.service.impl.ConsignmentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConsignmentServiceTest {

    @Mock
    private IShipmentService shipmentService;

    @Mock
    private ITaskParamService taskParamService;

    @InjectMocks
    private ConsignmentService consignmentService;

    private Task task1;
    private Task task2;
    private Task task3;
    private Shipment shipment1;
    private Shipment shipment2;
    private ParamValueShipmentDTO paramValue1;
    private ParamValueShipmentDTO paramValue2;

    @BeforeEach
    void setUp() {
        // Setup test data
        task1 = createTask("TASK_001");
        task2 = createTask("TASK_002");
        task3 = createTask("TASK_003");

        shipment1 = createShipment("SHIP_001", "EXT_CONSIGN_001", "EXT_ORDER_001");
        shipment2 = createShipment("SHIP_002", "EXT_CONSIGN_002", "EXT_ORDER_002");

        paramValue1 = createParamValueShipmentDTO("SHIP_001");
        paramValue2 = createParamValueShipmentDTO("SHIP_002");
    }

    @Test
    void getShipmentDetailsFromTask_WithValidTasksAndShipments_ShouldReturnCorrectMapping() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1, task2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(Arrays.asList(shipment1, shipment2));

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        assertTrue(result.containsKey("TASK_001"));
        assertTrue(result.containsKey("TASK_002"));

        ShipmentDetailsDTO details1 = result.get("TASK_001");
        assertEquals("SHIP_001", details1.getShipmentCode());
        assertEquals("EXT_CONSIGN_001", details1.getExternalConsignmentId());
        assertEquals("EXT_ORDER_001", details1.getExternalCustomerOrderId());

        ShipmentDetailsDTO details2 = result.get("TASK_002");
        assertEquals("SHIP_002", details2.getShipmentCode());
        assertEquals("EXT_CONSIGN_002", details2.getExternalConsignmentId());
        assertEquals("EXT_ORDER_002", details2.getExternalCustomerOrderId());

        verify(taskParamService).getShipmentCode(task1);
        verify(taskParamService).getShipmentCode(task2);
        verify(shipmentService).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void getShipmentDetailsFromTask_WithEmptyTaskList_ShouldReturnEmptyMap() {
        // Arrange
        List<Task> tasks = Collections.emptyList();

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService, never()).getShipmentCode(any());
    }

    @Test
    void getShipmentDetailsFromTask_WithTasksHavingNoShipmentCode_ShouldReturnEmptyMap() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1, task2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.empty());
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.empty());

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService).getShipmentCode(task1);
        verify(taskParamService).getShipmentCode(task2);
    }

    @Test
    void getShipmentDetailsFromTask_WithMixedTasksAndShipmentCodes_ShouldReturnOnlyValidMappings() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1, task2, task3);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.empty());
        when(taskParamService.getShipmentCode(task3)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(Arrays.asList(shipment1, shipment2));

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        assertTrue(result.containsKey("TASK_001"));
        assertFalse(result.containsKey("TASK_002")); // This task had no shipment code
        assertTrue(result.containsKey("TASK_003"));

        verify(taskParamService).getShipmentCode(task1);
        verify(taskParamService).getShipmentCode(task2);
        verify(taskParamService).getShipmentCode(task3);
        verify(shipmentService).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void getShipmentDetailsFromTask_WithShipmentCodesButNoMatchingShipments_ShouldReturnEmptyMap() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1, task2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(Collections.emptyList());

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService).getShipmentCode(task1);
        verify(taskParamService).getShipmentCode(task2);
        verify(shipmentService).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void getShipmentDetailsFromTask_WithPartialMatchingShipments_ShouldReturnOnlyMatchingMappings() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1, task2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(Arrays.asList(shipment1)); // Only one shipment returned

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        assertTrue(result.containsKey("TASK_001"));
        assertFalse(result.containsKey("TASK_002")); // No matching shipment for this task

        ShipmentDetailsDTO details = result.get("TASK_001");
        assertEquals("SHIP_001", details.getShipmentCode());
        assertEquals("EXT_CONSIGN_001", details.getExternalConsignmentId());
        assertEquals("EXT_ORDER_001", details.getExternalCustomerOrderId());

        verify(shipmentService).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void getShipmentDetailsFromTask_WithDuplicateShipmentCodes_ShouldHandleCorrectly() {
        // Arrange
        ParamValueShipmentDTO duplicateParamValue = createParamValueShipmentDTO("SHIP_001");
        List<Task> tasks = Arrays.asList(task1, task2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(duplicateParamValue)); // Same shipment code
        when(shipmentService.getAllByCodes(anySet())).thenReturn(Arrays.asList(shipment1));

        // Act
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        assertTrue(result.containsKey("TASK_001"));
        assertTrue(result.containsKey("TASK_002"));

        // Both tasks should map to the same shipment details
        ShipmentDetailsDTO details1 = result.get("TASK_001");
        ShipmentDetailsDTO details2 = result.get("TASK_002");

        assertEquals(details1.getShipmentCode(), details2.getShipmentCode());
        assertEquals(details1.getExternalConsignmentId(), details2.getExternalConsignmentId());
        assertEquals(details1.getExternalCustomerOrderId(), details2.getExternalCustomerOrderId());

        verify(shipmentService).getAllByCodes(Set.of("SHIP_001")); // Only one unique shipment code
    }

    @Test
    void getShipmentDetailsFromTask_WithNullTasks_ShouldThrowException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(null);
        });
    }
    @Test
    void getShipmentDetailsFromTask_WithShipmentServiceException_ShouldPropagateException() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(shipmentService.getAllByCodes(anySet())).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(tasks);
        });

        assertEquals("Database error", exception.getMessage());
    }

    @Test
    void getShipmentDetailsFromTask_WithTaskParamServiceException_ShouldPropagateException() {
        // Arrange
        List<Task> tasks = Arrays.asList(task1);

        when(taskParamService.getShipmentCode(task1)).thenThrow(new RuntimeException("Task param error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(tasks);
        });

        assertEquals("Task param error", exception.getMessage());
    }

    // Helper methods for creating test objects
    private Task createTask(String code) {
        Task task = new Task();
        task.setCode(code);
        return task;
    }

    private Shipment createShipment(String code, String externalConsignmentId, String externalCustomerOrderId) {
        Shipment shipment = new Shipment();
        shipment.setCode(code);
        shipment.setExternalConsignmentId(externalConsignmentId);
        shipment.setExternalCustomerOrderId(externalCustomerOrderId);
        return shipment;
    }

    private ParamValueShipmentDTO createParamValueShipmentDTO(String code) {
        ParamValueShipmentDTO dto = new ParamValueShipmentDTO();
        dto.setCode(code);
        return dto;
    }
}