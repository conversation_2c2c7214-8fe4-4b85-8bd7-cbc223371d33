package com.dpw.ctms.move.fakers;

import com.dpw.ctms.move.dto.EventRequestDTO;
import com.dpw.ctms.move.dto.StateConfigDTO;
import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.dto.TransitionConfigDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.dpw.ctms.move.enums.TripStatus;
import com.github.javafaker.Faker;

import java.util.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Fakers {

    public static final Faker faker = new Faker();

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMap(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    public static Map<String, StateMachineTenantDTO> createStateMachineTenantConfigMapWithoutInitialState(String tenantId) {
        StateMachineTenantDTO stateMachineTenantDTO = createStateMachineTenantDTO();
        for (StateConfigDTO stateConfigDTO : stateMachineTenantDTO.getTask().getStates()) {
            stateConfigDTO.setIsInitial(false);
        }
        Map<String, StateMachineTenantDTO> map = new HashMap<>();
        map.put(tenantId, stateMachineTenantDTO);
        return map;
    }

    private static StateMachineTenantDTO createStateMachineTenantDTO() {
        return StateMachineTenantDTO.builder()
                .task(createStateTransitionHolder(StateMachineEntityType.TASK))
                .shipment(createStateTransitionHolder(StateMachineEntityType.SHIPMENT))
                .trip(createStateTransitionHolder(StateMachineEntityType.TRIP)).build();
    }

    private static StateTransitionHolderDTO createStateTransitionHolder(StateMachineEntityType stateMachineEntityType) {
        return StateTransitionHolderDTO.builder().states(createStateConfig(stateMachineEntityType))
                .transitions(createTransitionConfig(stateMachineEntityType)).build();
    }

    private static List<StateConfigDTO> createStateConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    StateConfigDTO.builder().state("CREATED").isInitial(true).build(),
                    StateConfigDTO.builder().state("COMPLETED").isInitial(false).build(),
                    StateConfigDTO.builder().state("CLOSED").isInitial(false).build()
            );
            case SHIPMENT -> List.of(
                    StateConfigDTO.builder().state("ASSIGNED").isInitial(true).build(),
                    StateConfigDTO.builder().state("ALLOCATED").isInitial(false).build(),
                    StateConfigDTO.builder().state("IN_TRANSIT").isInitial(false).build(),
                    StateConfigDTO.builder().state("DELIVERED").isInitial(false).build(),
                    StateConfigDTO.builder().state("DELIVERED_WITH_EXCEPTION").isInitial(false).build(),
                    StateConfigDTO.builder().state("CANCELLED").isInitial(false).build()
            );
            case TRIP -> List.of(
                    StateConfigDTO.builder().state("CREATED").isInitial(true).build(),
                    StateConfigDTO.builder().state("IN_PROGRESS").isInitial(false).build(),
                    StateConfigDTO.builder().state("COMPLETED").isInitial(false).build(),
                    StateConfigDTO.builder().state("COMPLETED_WITH_EXCEPTIONS").isInitial(false).build(),
                    StateConfigDTO.builder().state("CANCELLED").isInitial(false).build(),
                    StateConfigDTO.builder().state("CLOSED").isInitial(false).build()
            );
        };
    }

    private static List<TransitionConfigDTO> createTransitionConfig(StateMachineEntityType stateMachineEntityType) {
        return switch (stateMachineEntityType) {
            case TASK -> List.of(
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("COMPLETED")
                            .event("TASK_COMPLETED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED").targetState("CLOSED")
                            .event("TASK_CLOSED")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("CLOSED")
                            .event("TASK_CLOSED")
                            .guardId("TASK_TO_CLOSED_GUARD_ID")
                            .actionId("").build()
            );
            case SHIPMENT -> List.of(TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("ALLOCATED")
                            .event("RESOURCE_ALLOCATED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ALLOCATED").targetState("IN_TRANSIT")
                            .event("PICKED_UP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_TRANSIT").targetState("DELIVERED")
                            .event("DELIVERED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_TRANSIT").targetState("DELIVERED_WITH_EXCEPTION")
                            .event("DELIVERED_WITH_EXCEPTION")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("IN_TRANSIT")
                            .event("PICKED_UP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ASSIGNED").targetState("CANCELLED")
                            .event("CANCELLED")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("ALLOCATED").targetState("CANCELLED")
                            .event("CANCELLED")
                            .guardId("")
                            .actionId("").build()
            );
            case TRIP -> List.of(TransitionConfigDTO.builder().sourceState("CREATED").targetState("IN_PROGRESS")
                            .event("START_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_PROGRESS").targetState("COMPLETED")
                            .event("END_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("IN_PROGRESS").targetState("COMPLETED_WITH_EXCEPTIONS")
                            .event("END_TRIP_WITH_EXCEPTION")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED").targetState("CLOSED")
                            .event("CLOSE_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("COMPLETED_WITH_EXCEPTIONS").targetState("CLOSED")
                            .event("CLOSE_TRIP")
                            .guardId("")
                            .actionId("").build(),
                    TransitionConfigDTO.builder().sourceState("CREATED").targetState("CANCELLED")
                            .event("CANCEL_TRIP")
                            .guardId("")
                            .actionId("").build()
            );
        };
    }

    public static Task createTask() {
        return Task.builder().code(faker.lorem().sentence())
                .externalTaskMasterCode(faker.lorem().sentence())
                .sequence(1)
                .expectedStartAt(System.currentTimeMillis())
                .expectedEndAt(System.currentTimeMillis())
                .status(TaskStatus.CREATED).build();
    }


    public static Trip createBOLTestTrip() {
        Trip trip = createTrip("BOL_TEST_TRIP", TripStatus.CREATED);
        trip.setExternalOriginLocationCode("ORIGIN_LOC");
        trip.setExternalDestinationLocationCode("DEST_LOC");

        // Create shipment with specific external consignment ID
        Shipment shipment = createShipment("BOL_SHIPMENT", trip);
        shipment.setId(100L);
        shipment.setExternalConsignmentId("CONS001");
        trip.setShipments(Set.of(shipment));

        // Create stop with specific location code
        Stop stop = createStop("BOL_STOP", "STOP_LOC", 1, trip);
        trip.setStops(Set.of(stop));

        return trip;
    }

    public static Shipment createShipment(String code, Trip trip) {
        return Shipment.builder()
                .code(code)
                .status(ShipmentStatus.ASSIGNED)
                .externalConsignmentId("CONS_" + faker.number().digits(3))
                .expectedPickupAt(System.currentTimeMillis() + 1800000L)
                .expectedDeliveryAt(System.currentTimeMillis() + 5400000L)
                .trip(trip)
                .build();
    }

    public static Task createTask(String code, TaskStatus status) {
        return Task.builder()
                .code(code)
                .status(status)
                .externalTaskMasterCode(faker.regexify("[A-Z0-9]{10}"))
                .sequence(faker.number().numberBetween(1, 10))
                .expectedStartAt(System.currentTimeMillis())
                .expectedEndAt(System.currentTimeMillis() + 3600000L)
                .externalTaskRegistrationCode(faker.regexify("EXT-REG-[0-9]{3}"))
                .build();
    }

    public static Shipment createShipment(String code, ShipmentStatus status) {
        return Shipment.builder()
                .code(code)
                .status(status)
                .externalConsignmentId(faker.regexify("CONS-[0-9]{6}"))
                .externalCustomerOrderId(faker.regexify("ORDER-[0-9]{6}"))
                .expectedPickupAt(System.currentTimeMillis())
                .expectedDeliveryAt(System.currentTimeMillis() + 7200000L)
                .weight(BigDecimal.valueOf(faker.number().randomDouble(2, 1, 1000)))
                .weightUom("kg")
                .volume(BigDecimal.valueOf(faker.number().randomDouble(2, 1, 100)))
                .volumeUom("m3")
                .build();
    }

    public static Trip createTrip(String code, TripStatus status) {
        Trip trip = new Trip();
        trip.setCode(code);
        trip.setStatus(status);
        trip.setExternalOriginLocationCode(faker.regexify("LOC-[A-Z0-9]{6}"));
        trip.setExternalDestinationLocationCode(faker.regexify("LOC-[A-Z0-9]{6}"));
        trip.setExpectedStartAt(System.currentTimeMillis());
        trip.setExpectedEndAt(System.currentTimeMillis() + 14400000L);
        return trip;
    }

    public static Stop createStop(String code, String locationCode, int sequence, Trip trip) {
        return Stop.builder()
                .code(code)
                .externalLocationCode(locationCode)
                .sequence(sequence)
                .status(StopStatus.PLANNED)
                .trip(trip)
                .build();
    }

    private static List<ConsignmentRecord.Product> createConsignmentProducts() {
        List<ConsignmentRecord.Product> products = new ArrayList<>();

        // Product 1 - Electronics
        ConsignmentRecord.Product product1 = createConsignmentProduct(
                1L,
                "Electronics",
                List.of(
                        createProductProperty("Weight", 15.5, "KG"),
                        createProductProperty("Volume", 0.02, "CBM"),
                        createProductProperty("Quantity", 10.0, "PCS")
                )
        );

        // Product 2 - Textiles
        ConsignmentRecord.Product product2 = createConsignmentProduct(
                2L,
                "Textiles",
                List.of(
                        createProductProperty("Weight", 25.0, "KG"),
                        createProductProperty("Volume", 0.05, "CBM"),
                        createProductProperty("Quantity", 50.0, "PCS")
                )
        );

        products.add(product1);
        products.add(product2);

        return products;
    }


    public static Trip createTrip() {
        return new Trip(
                faker.lorem().characters(), // code
                TripStatus.CREATED, // status
                faker.lorem().word(), // externalOriginLocationCode
                faker.lorem().word(), // externalDestinationLocationCode
                System.currentTimeMillis(), // expectedStartAt
                System.currentTimeMillis(), // expectedEndAt
                null,
                null, // actualEndAt
                null, // details (JsonNode)
                null, // shipments (Set<Shipment>)
                null, // stops (Set<Stop>)
                null, // vehicleResource (VehicleResource)
                null, // trailerResources (Set<TrailerResource>)
                null, // vehicleOperatorResources (Set<VehicleOperatorResource>)
                null, // transportOrder (TransportOrder)
                null  // exceptions (Set<Exception>)
        );
    }

    // EventRequestDTO creation methods
    public static <T> EventRequestDTO<T> createEventRequestDTO(T originalEntity, T updatedEntity, String entityType) {
        return EventRequestDTO.<T>builder()
                .entityType(entityType)
                .originalEntity(originalEntity)
                .updatedEntity(updatedEntity)
                .eventType("STATUS_UPDATE")
                .comments(faker.lorem().sentence())
                .build();
    }

    public static EventRequestDTO<Shipment> createShipmentEventRequestDTO(String shipmentCode,
                                                                           ShipmentStatus fromStatus,
                                                                           ShipmentStatus toStatus) {
        Shipment originalShipment = createShipment(shipmentCode, fromStatus);
        Shipment updatedShipment = createShipment(shipmentCode, toStatus);
        updatedShipment.setActualPickupAt(System.currentTimeMillis() - 3600000L);
        updatedShipment.setActualDeliveryAt(System.currentTimeMillis());
        updatedShipment.setUpdatedBy(faker.name().username());
        updatedShipment.setUpdatedAt(System.currentTimeMillis());

        return createEventRequestDTO(originalShipment, updatedShipment, StateMachineEntityType.SHIPMENT.name());
    }

    public static EventRequestDTO<Task> createTaskEventRequestDTO(String taskCode,
                                                                  TaskStatus fromStatus,
                                                                  TaskStatus toStatus) {
        Task originalTask = createTask(taskCode, fromStatus);
        Task updatedTask = createTask(taskCode, toStatus);
        updatedTask.setActualStartAt(System.currentTimeMillis() - 3600000L);
        updatedTask.setActualEndAt(System.currentTimeMillis());
        updatedTask.setUpdatedBy(faker.name().username());
        updatedTask.setUpdatedAt(System.currentTimeMillis());

        return createEventRequestDTO(originalTask, updatedTask, StateMachineEntityType.TASK.name());
    }

    public static EventRequestDTO<Trip> createTripEventRequestDTO(String tripCode,
                                                                  TripStatus fromStatus,
                                                                  TripStatus toStatus) {
        Trip originalTrip = createTrip(tripCode, fromStatus);
        Trip updatedTrip = createTrip(tripCode, toStatus);
        updatedTrip.setActualStartAt(System.currentTimeMillis() - 3600000L);
        updatedTrip.setActualEndAt(System.currentTimeMillis());
        updatedTrip.setUpdatedBy(faker.name().username());
        updatedTrip.setUpdatedAt(System.currentTimeMillis());

        return createEventRequestDTO(originalTrip, updatedTrip, StateMachineEntityType.TRIP.name());
    }

    // Default event request DTOs with common transitions
    public static EventRequestDTO<Shipment> createDefaultShipmentEventRequestDTO() {
        return createShipmentEventRequestDTO("SHP001", ShipmentStatus.ASSIGNED, ShipmentStatus.ALLOCATED);
    }

    public static EventRequestDTO<Task> createDefaultTaskEventRequestDTO() {
        return createTaskEventRequestDTO("TASK001", TaskStatus.CREATED, TaskStatus.COMPLETED);
    }

    public static EventRequestDTO<Trip> createDefaultTripEventRequestDTO() {
        return createTripEventRequestDTO("TRIP001", TripStatus.CREATED, TripStatus.COMPLETED);
    }

    // Enhanced entity creation with specific attributes
    public static Shipment createShipmentWithCompleteData(String code, ShipmentStatus status) {
        Shipment shipment = createShipment(code, status);
        shipment.setActualPickupAt(System.currentTimeMillis() - 7200000L);
        shipment.setActualDeliveryAt(System.currentTimeMillis());
        shipment.setUpdatedBy(faker.name().username());
        shipment.setUpdatedAt(System.currentTimeMillis());
        return shipment;
    }

    public static Task createTaskWithCompleteData(String code, TaskStatus status) {
        Task task = createTask(code, status);
        task.setActualStartAt(System.currentTimeMillis() - 3600000L);
        task.setActualEndAt(System.currentTimeMillis());
        task.setUpdatedBy(faker.name().username());
        task.setUpdatedAt(System.currentTimeMillis());
        task.setExternalTaskRegistrationCode("EXT-REG-" + faker.number().digits(3));
        return task;
    }

    public static Trip createTripWithCompleteData(String code, TripStatus status) {
        Trip trip = createTrip(code, status);
        trip.setActualStartAt(System.currentTimeMillis() - 3600000L);
        trip.setActualEndAt(System.currentTimeMillis());
        trip.setUpdatedBy(faker.name().username());
        trip.setUpdatedAt(System.currentTimeMillis());
        return trip;
    }

    public static Shipment createShipment() {
        return Shipment.builder()
                .code(faker.lorem().characters())
                .status(ShipmentStatus.ASSIGNED)
                .actualPickupAt(null)
                .actualDeliveryAt(null)
                .expectedDeliveryAt(System.currentTimeMillis())
                .expectedPickupAt(System.currentTimeMillis())
                .build();
    }

    public static OmsListResponse<ConsignmentRecord> createOmsConsignmentResponse() {
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();
        ConsignmentRecord record = createCompleteConsignmentRecord("CONS001");
        response.setResponse(List.of(record));
        response.setTotalElements(1L);
        return response;
    }

    public static ConsignmentRecord createCompleteConsignmentRecord(String code) {
        Long currentTime = System.currentTimeMillis();

        // Create products with nested details
        List<ConsignmentRecord.Product> products = createConsignmentProducts();

        // Create customer order metadata
        ConsignmentRecord.CustomerOrderMetadata customerOrderMetadata = ConsignmentRecord.CustomerOrderMetadata.builder()
                .customerOrderNumber("CO-" + faker.number().digits(6))
                .internalReferenceNumber("REF-" + faker.number().digits(8))
                .build();

        // Create line item metadata
        ConsignmentRecord.LineItemMetadata lineItemMetadata = ConsignmentRecord.LineItemMetadata.builder()
                .specialInstructions("Handle with care - " + faker.lorem().sentence())
                .lineIdentifierNumber1("LIN1-" + faker.number().digits(5))
                .lineIdentifierNumber2("LIN2-" + faker.number().digits(5))
                .build();

        // Create customer
        ConsignmentRecord.Customer customer = ConsignmentRecord.Customer.builder()
                .id("CUST-" + faker.number().digits(4))
                .customerType(LabelValue.builder()
                        .label("Corporate")
                        .value("CORP")
                        .build())
                .build();

        // Create locations
        ConsignmentRecord.Location origin = ConsignmentRecord.Location.builder()
                .id("LOC-ORIGIN-" + faker.number().digits(3))
                .build();

        ConsignmentRecord.Location destination = ConsignmentRecord.Location.builder()
                .id("LOC-DEST-" + faker.number().digits(3))
                .build();

        // Create time info
        ConsignmentRecord.TimeInfo expectedPickupTime = ConsignmentRecord.TimeInfo.builder()
                .epoch(currentTime + 3600000L) // 1 hour from now
                .build();

        ConsignmentRecord.TimeInfo expectedDeliveryTime = ConsignmentRecord.TimeInfo.builder()
                .epoch(currentTime + 86400000L) // 24 hours from now
                .build();

        // Create consignment metadata
        Map<String, String> consignmentMetadata = new HashMap<>();
        consignmentMetadata.put("priority", "HIGH");
        consignmentMetadata.put("temperature_controlled", "true");
        consignmentMetadata.put("hazardous", "false");

        return ConsignmentRecord.builder()
                .id(faker.number().randomNumber(6, true))
                .code(code)
                .lineItemId(faker.number().randomNumber(5, true))
                .lineItemCode("LI-" + faker.number().digits(6))
                .customerOrderId(faker.number().randomNumber(5, true))
                .customerOrderCode("CO-" + faker.number().digits(6))
                .customerOrderMetadata(customerOrderMetadata)
                .lineItemMetadata(lineItemMetadata)
                .consignmentMetadata(consignmentMetadata)
                .customer(customer)
                .origin(origin)
                .destination(destination)
                .expectedPickupTime(expectedPickupTime)
                .expectedDeliveryTime(expectedDeliveryTime)
                .status(LabelValue.builder()
                        .label("Confirmed")
                        .value("CONFIRMED")
                        .build())
                .movementType(LabelValue.builder()
                        .label("Delivery")
                        .value("DELIVERY")
                        .build())
                .createdAt(currentTime - 3600000L) // 1 hour ago
                .updatedAt(currentTime)
                .createdBy("system")
                .updatedBy("system")
                .products(products)
                .build();
    }

    private static ConsignmentRecord.Product createConsignmentProduct(Long id, String category, List<ConsignmentRecord.Product.Property> properties) {
        ConsignmentRecord.Product.ResourceDetails resourceDetails = ConsignmentRecord.Product.ResourceDetails.builder()
                .id(id)
                .build();

        return ConsignmentRecord.Product.builder()
                .id(id)
                .resourceDetails(resourceDetails)
                .properties(properties)
                .build();
    }

    private static ConsignmentRecord.Product.Property createProductProperty(String propertyName, double value, String uomCode) {
        ConsignmentRecord.Product.Property.UnitOfMeasurement uom = ConsignmentRecord.Product.Property.UnitOfMeasurement.builder()
                .id(faker.number().randomNumber(3, true))
                .build();

        return ConsignmentRecord.Product.Property.builder()
                .propertyName(LabelValue.builder()
                        .label(propertyName)
                        .value(propertyName.toUpperCase())
                        .build())
                .propertyValue(value)
                .unitOfMeasurement(uom)
                .build();
    }

    public static ListResponse<FacilityRecord> createFacilityResponse() {
        ListResponse<FacilityRecord> response = new ListResponse<>();

        FacilityRecord originFacility = FacilityRecord.builder()
                .id(1L)
                .code("ORIGIN_LOC")
                .name("Origin Facility")
                .status(LabelValue.builder()
                        .label("Active")
                        .value("ACTIVE")
                        .build())
                .ownershipType(LabelValue.builder()
                        .label("Company Owned")
                        .value("COMPANY_OWNED")
                        .build())
                .instructions("Priority handling required")
                .build();

        FacilityRecord destFacility = FacilityRecord.builder()
                .id(2L)
                .code("DEST_LOC")
                .name("Destination Facility")
                .status(LabelValue.builder()
                        .label("Active")
                        .value("ACTIVE")
                        .build())
                .ownershipType(LabelValue.builder()
                        .label("Third Party")
                        .value("THIRD_PARTY")
                        .build())
                .instructions("Standard handling")
                .build();

        response.setRecords(List.of(originFacility, destFacility));
        response.setTotalElements(2L);
        return response;
    }

    public static ListResponse<UomRecord> createUomResponse() {
        ListResponse<UomRecord> response = new ListResponse<>();

        UomRecord kilogram = UomRecord.builder()
                .id(1L)
                .name("Kilogram")
                .code("KG")
                .type("WEIGHT")
                .build();

        UomRecord cubicMeter = UomRecord.builder()
                .id(2L)
                .name("Cubic Meter")
                .code("CBM")
                .type("VOLUME")
                .build();

        UomRecord pieces = UomRecord.builder()
                .id(3L)
                .name("Pieces")
                .code("PCS")
                .type("QUANTITY")
                .build();

        response.setRecords(List.of(kilogram, cubicMeter, pieces));
        response.setTotalElements(3L);
        return response;
    }

    public static OmsListResponse<ConsignmentRecord> createEmptyOmsResponse() {
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();
        response.setResponse(Collections.emptyList());
        return response;
    }

    public static ListResponse<FacilityRecord> createEmptyFacilityResponse() {
        ListResponse<FacilityRecord> response = new ListResponse<>();
        response.setRecords(Collections.emptyList());
        response.setTotalElements(0L);
        return response;
    }

    public static Trip createBOLTestTripWithMultipleShipments() {
        Trip trip = createTrip("MULTI_SHIPMENT_TRIP", TripStatus.CLOSED);
        trip.setExternalOriginLocationCode("ORIGIN_LOC");
        trip.setExternalDestinationLocationCode("DEST_LOC");

        Set<Shipment> shipments = new HashSet<>();
        for (int i = 1; i <= 5; i++) {
            Shipment shipment = createShipment("SHIPMENT_" + i, trip);
            shipment.setId(100L + i);
            shipment.setExternalConsignmentId("CONS00" + i);
            shipments.add(shipment);
        }
        trip.setShipments(shipments);

        Stop stop = createStop("MULTI_STOP", "STOP_LOC", 1, trip);
        trip.setStops(Set.of(stop));

        return trip;
    }

    public static OmsListResponse<ConsignmentRecord> createLargeOmsConsignmentResponse() {
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();

        List<ConsignmentRecord> records = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            String code = "CONS00" + i;
            ConsignmentRecord record = createCompleteConsignmentRecord(code);
            records.add(record);
        }

        response.setResponse(records);
        response.setTotalElements((long) records.size());
        return response;
    }
}
