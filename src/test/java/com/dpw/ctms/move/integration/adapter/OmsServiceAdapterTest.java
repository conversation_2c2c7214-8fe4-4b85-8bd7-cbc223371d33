package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.constants.OmsServiceConstants;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.feignClient.OmsClient;
import com.dpw.ctms.move.integration.request.oms.OmsListRequest;
import com.dpw.ctms.move.integration.response.oms.OmsApiResponse;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.exception.TMSFeignException;
import org.springframework.http.HttpHeaders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class OmsServiceAdapterTest extends BaseTest {

    @Autowired
    private OmsServiceAdapter omsServiceAdapter;

    @MockBean
    private OmsClient omsClient;

    private ConsignmentListRequestDTO requestDTO;

    @BeforeEach
    void setUp() {
        requestDTO = ConsignmentListRequestDTO.builder()
                .ids(Arrays.asList("CONS001", "CONS002"))
                .build();
    }

    @Test
    void shouldGetConsignmentListSuccessfully() {
        
        ConsignmentRecord record1 = ConsignmentRecord.builder()
                .id(1L)
                .code("CONS001")
                .build();

        ConsignmentRecord record2 = ConsignmentRecord.builder()
                .id(2L)
                .code("CONS002")
                .build();

        OmsListResponse<ConsignmentRecord> listResponse = new OmsListResponse<>();
        listResponse.setResponse(Arrays.asList(record1, record2));
        listResponse.setTotalElements(2L);

        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(listResponse);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);

        
        OmsListResponse<ConsignmentRecord> result = omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10));

       
        assertNotNull(result);
        assertEquals(2, result.getResponse().size());
        assertEquals("CONS001", result.getResponse().get(0).getCode());
        assertEquals("CONS002", result.getResponse().get(1).getCode());
        assertEquals(2, result.getTotalElements());

        verify(omsClient).getConsignmentList(any(OmsListRequest.class));
    }

    @Test
    void shouldHandleEmptyConsignmentList() {
        
        OmsListResponse<ConsignmentRecord> emptyListResponse = new OmsListResponse<>();
        emptyListResponse.setResponse(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(emptyListResponse);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);
        
        OmsListResponse<ConsignmentRecord> result = omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10));

       
        assertNotNull(result);
        assertTrue(result.getResponse().isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void shouldHandleNullResponseEntity() {
        
        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(null);

        OmsListResponse<ConsignmentRecord> result = omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10));

        assertNotNull(result);
        assertNull(result.getResponse());
    }

    @Test
    void shouldPropagateNotFoundExceptionCorrectly() {
        TMSFeignException notFoundException = new TMSFeignException(404, new HttpHeaders(), "Not found");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(notFoundException);

        TMSException exception = assertThrows(TMSException.class, () -> 
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10)));
        assertEquals(EXTERNAL_INVOCATION_EXCEPTION.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateBadRequestExceptionCorrectly() {
        TMSFeignException badRequestException = new TMSFeignException(400, new HttpHeaders(), "Bad Request");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(badRequestException);

        TMSException exception = assertThrows(TMSException.class, () ->
                omsServiceAdapter.getConsignmentList(requestDTO, new PaginationDTO(0,10)));
        assertEquals(INVALID_REQUEST.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateForbiddenExceptionCorrectly() {

        TMSFeignException forbiddenException = new TMSFeignException(403, new HttpHeaders(), "Forbidden");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(forbiddenException);

        TMSException exception = assertThrows(TMSException.class, () -> 
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10)));
        assertEquals(INTEGRATION_ERROR.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateServiceUnavailableExceptionCorrectly() {

        TMSFeignException serviceUnavailableException = new TMSFeignException(503, new HttpHeaders(), "Service Unavailable");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(serviceUnavailableException);

        TMSException exception = assertThrows(TMSException.class, () -> 
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10)));
        assertEquals(EXTERNAL_INVOCATION_EXCEPTION.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateInternalServerErrorExceptionCorrectly() {

        TMSFeignException internalServerErrorException = new TMSFeignException(500, new HttpHeaders(), "Internal Server Error");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(internalServerErrorException);

        TMSException exception = assertThrows(TMSException.class, () -> 
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10)));
        assertEquals(INTERNAL_ERROR.name(), exception.getErrorCode());
    }

    @Test
    void shouldHandleUnexpectedExceptionCorrectly() {
        RuntimeException unexpectedException = new RuntimeException("Unexpected error");

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenThrow(unexpectedException);

        assertThrows(GenericException.class, () ->
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10)));
    }

    @Test
    void shouldReturnCorrectServiceName() {
        String serviceName = omsServiceAdapter.getServiceName();
        assertEquals(OmsServiceConstants.OMS_SERVICE, serviceName);
    }

    @Test
    void shouldHandlePaginationParametersCorrectly() {
        
        OmsListResponse<ConsignmentRecord> paginatedResponse = new OmsListResponse<>();
        paginatedResponse.setResponse(Collections.emptyList());
        paginatedResponse.setTotalElements(100L);

        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(paginatedResponse);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);

        OmsListResponse<ConsignmentRecord> result = 
                omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(2,20));

        assertNotNull(result);
        verify(omsClient).getConsignmentList(argThat(request -> {
            return request.getPagination().getPageNo() == 2 &&
                   request.getPagination().getPageSize() == 20;
        }));
    }

    @Test
    void shouldCreateCorrectRequestStructure() {
        OmsListResponse<ConsignmentRecord> response = Fakers.createOmsConsignmentResponse();
        
        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(response);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);
        omsServiceAdapter.getConsignmentList(requestDTO,  new PaginationDTO(0,10));

        verify(omsClient).getConsignmentList(argThat(request -> {
            assertNotNull(request.getFilter());
            assertNotNull(request.getPagination());
            assertTrue(request.getFilter().getIds().containsAll(requestDTO.getIds()));
            return true;
        }));
    }

    @Test
    void shouldHandleEmptyIdList() {
        
        ConsignmentListRequestDTO emptyRequest = ConsignmentListRequestDTO.builder()
                .ids(Collections.emptyList())
                .build();

        OmsListResponse<ConsignmentRecord> emptyResponse = Fakers.createEmptyOmsResponse();
        
        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(emptyResponse);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);
        OmsListResponse<ConsignmentRecord> result = 
                omsServiceAdapter.getConsignmentList(emptyRequest,  new PaginationDTO(0,10));
        assertNotNull(result);
        assertTrue(result.getResponse().isEmpty());
    }

    @Test
    void shouldHandleNullIdList() {
        ConsignmentListRequestDTO nullIdsRequest = ConsignmentListRequestDTO.builder()
                .ids(null)
                .build();

        OmsListResponse<ConsignmentRecord> emptyResponse = Fakers.createEmptyOmsResponse();
        
        OmsApiResponse<OmsListResponse<ConsignmentRecord>> apiResponse = new OmsApiResponse<>();
        apiResponse.setData(emptyResponse);

        ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(omsClient.getConsignmentList(any(OmsListRequest.class)))
                .thenReturn(responseEntity);
        OmsListResponse<ConsignmentRecord> result = 
                omsServiceAdapter.getConsignmentList(nullIdsRequest,  new PaginationDTO(0,10));
        assertNotNull(result);
        assertTrue(result.getResponse().isEmpty());
    }
}