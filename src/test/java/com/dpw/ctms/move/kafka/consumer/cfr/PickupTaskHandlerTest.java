package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorHeaderDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.cfr.PickupTaskMessageDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@SpringBootTest
@Transactional
@AutoConfigureMockMvc
class PickupTaskHandlerTest extends BaseTest {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IShipmentService shipmentService;
    @Autowired
    private ITripService tripService;
    @Autowired
    private PickupTaskHandler pickupTaskHandler;
    @MockBean
    private StateMachineServiceRegistry stateMachineServiceRegistry;
    @MockBean
    private StateMachineConfigReader stateMachineConfigReader;

    @BeforeEach
    void mockStateMachine() {
        IStateMachineService<?> mockStateMachine = Mockito.mock(IStateMachineService.class);
        Mockito.doNothing().when(mockStateMachine).handleEvent(Mockito.any(), Mockito.any(), Mockito.any());
        when(stateMachineServiceRegistry.getService(Mockito.any(StateMachineEntityType.class)))
                .thenAnswer(invocation -> mockStateMachine);
    }

    @Test
    void testHandle_withValidShipment() {

        Trip trip = new Trip();
        trip.setActualStartAt(null);

        // Create and initialize 2 stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP1");
        shipment.setTrip(trip);
        shipment.setActualPickupAt(null);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP1");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK123");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        PickupTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = PickupTaskMessageDTO.PercolatedRecordDTO.builder()
                .loadingCompletionTime(1718600000000L)
                .arrivalTime(1718500000000L)
                .build();
        IntegratorMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK123")
                        .status(TaskStatus.CLOSED.name())
                        .build();
        IntegratorMessageRequestDTO.MessageRequestDTO<PickupTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorMessageRequestDTO.MessageRequestDTO.<PickupTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();
        PickupTaskMessageDTO pickupTaskMessageDTO = PickupTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(1718600000000L).build())
                .build();
        pickupTaskHandler.handle(pickupTaskMessageDTO);
        Shipment updatedShipment = shipmentService.findShipmentById(shipment.getId());
        Trip updatedTrip = tripService.findTripById(trip.getId());
        Long expectedPickupEpoch = percolatedRecordDTO.getLoadingCompletionTime();
        Long expectedStartEpoch = percolatedRecordDTO.getLoadingCompletionTime();
        assertThat(updatedShipment.getActualPickupAt()).isEqualTo(expectedPickupEpoch);
        assertThat(updatedTrip.getActualStartAt()).isEqualTo(expectedStartEpoch);
    }
}