package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;


import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ShipmentViewIntegrationTest extends IntegrationTestBase {

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    @Autowired
    private StopRepository stopRepository;

    private String uniqueId;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        uniqueId = String.valueOf(System.currentTimeMillis());
        cleanup();
        setupComprehensiveTestData();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            shipmentRepository.deleteAll();
            tripRepository.deleteAll();
            transportOrderRepository.deleteAll();
            stopRepository.deleteAll();
        } catch (Exception e) {
            System.err.println("Repository cleanup failed, using database cleanup: " + e.getMessage());
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    void getShipmentView_WithCompleteShipment_ShouldCoverAllMappingMethods() throws Exception {
        // Arrange - This single test covers most mapping methods for maximum coverage
        TenantContext.setCurrentTenant("CFR");
        String shipmentCode = "SHIPMENT_BASIC_1_" + uniqueId;

        // Act
        MvcResult result = performGetShipmentViewRequest(shipmentCode);

        // Assert
        ShipmentViewResponse response = parseResponse(result);
        assertNotNull(response);
        assertEquals(shipmentCode, response.getShipmentCode());
        assertNotNull(response.getStatus());
        assertNotNull(response.getStatus().getLabel());
        assertNotNull(response.getStatus().getValue());

        // Verify response structure and mapping
        assertNotNull(response.getOriginStop());
        assertNotNull(response.getOriginStop().getCode());
        assertNotNull(response.getOriginStop().getExternalLocationCode());
        assertNotNull(response.getOriginStop().getStatus());

        assertNotNull(response.getDestinationStop());
        assertNotNull(response.getDestinationStop().getCode());
        assertNotNull(response.getDestinationStop().getExternalLocationCode());

        assertNotNull(response.getCustomerOrder());
        assertNotNull(response.getCustomerOrder().getCustomerOrderId());
        assertNotNull(response.getCustomerOrder().getConsignmentId());

        assertNotNull(response.getTransportOrder());
        assertNotNull(response.getTransportOrder().getTransportOrderCode());

        assertNotNull(response.getExpectedTimes());
        assertNotNull(response.getExpectedTimes().getStartAt());
        assertNotNull(response.getExpectedTimes().getEndAt());

        assertNotNull(response.getActualTimes());
        // Actual times should be null for this test case
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }

    @Test
    void getShipmentView_WithActualTimesAndDifferentStatus_ShouldCoverActualTimesMapping() throws Exception {
        // Arrange - Covers actual times mapping and different status scenarios
        TenantContext.setCurrentTenant("CFR");
        String shipmentCode = "SHIPMENT_COMPLEX_1_" + uniqueId;

        // Act
        MvcResult result = performGetShipmentViewRequest(shipmentCode);

        // Assert
        ShipmentViewResponse response = parseResponse(result);
        assertNotNull(response);
        assertEquals(shipmentCode, response.getShipmentCode());
        assertEquals(ShipmentStatus.ALLOCATED.name(), response.getStatus().getValue());
        assertEquals(ShipmentStatus.ALLOCATED.getDisplayName(), response.getStatus().getLabel());

        // Verify actual times are mapped (this shipment has actual times)
        assertNotNull(response.getActualTimes());
        assertNotNull(response.getActualTimes().getStartAt());
        assertNotNull(response.getActualTimes().getEndAt());
    }

    @Test
    void getShipmentView_WithMinimalShipmentData_ShouldHandleNullRelatedEntities() throws Exception {
        // Arrange - Covers null handling in mapper methods
        TenantContext.setCurrentTenant("CFR");
        String shipmentCode = "SHIPMENT_ADDITIONAL_" + uniqueId;

        // Act
        MvcResult result = performGetShipmentViewRequest(shipmentCode);

        // Assert
        ShipmentViewResponse response = parseResponse(result);
        assertNotNull(response);
        assertEquals(shipmentCode, response.getShipmentCode());
        assertEquals(ShipmentStatus.IN_TRANSIT.name(), response.getStatus().getValue());

        // Verify all related entities are mapped correctly
        assertNotNull(response.getOriginStop());
        assertNotNull(response.getDestinationStop());
        assertNotNull(response.getTransportOrder());
        assertNotNull(response.getCustomerOrder());
    }

    private void setupComprehensiveTestData() {
        TenantContext.setCurrentTenant("CFR");

        // Create Transport Orders
        TransportOrder basicTO = new TransportOrder();
        basicTO.setCode("TO_BASIC_" + uniqueId);
        basicTO.setStatus(TransportOrderStatus.ASSIGNED);
        basicTO.setAssignmentType(AssignmentType.EXTERNAL);
        basicTO.setAssigneeIdentifier("VENDOR_BASIC");
        basicTO = transportOrderRepository.save(basicTO);

        TransportOrder complexTO = new TransportOrder();
        complexTO.setCode("TO_COMPLEX_" + uniqueId);
        complexTO.setStatus(TransportOrderStatus.ASSIGNED);
        complexTO.setAssignmentType(AssignmentType.EXTERNAL);
        complexTO.setAssigneeIdentifier("VENDOR_COMPLEX");
        complexTO = transportOrderRepository.save(complexTO);

        // Create Trips
        Trip basicTrip = new Trip();
        basicTrip.setCode("TRIP_BASIC_1_" + uniqueId);
        basicTrip.setStatus(TripStatus.CREATED);
        basicTrip.setTransportOrder(basicTO);
        basicTrip.setExternalOriginLocationCode("ORIGIN_BASIC");
        basicTrip.setExternalDestinationLocationCode("DEST_BASIC");
        basicTrip = tripRepository.save(basicTrip);

        Trip complexTrip = new Trip();
        complexTrip.setCode("TRIP_COMPLEX_1_" + uniqueId);
        complexTrip.setStatus(TripStatus.CREATED);
        complexTrip.setTransportOrder(complexTO);
        complexTrip.setExternalOriginLocationCode("ORIGIN_COMPLEX");
        complexTrip.setExternalDestinationLocationCode("DEST_COMPLEX");
        complexTrip = tripRepository.save(complexTrip);

        // Create Stops
        Stop originStop = new Stop();
        originStop.setCode("STOP_ORIGIN_" + uniqueId);
        originStop.setExternalLocationCode("ORIGIN_BASIC");
        originStop.setStatus(StopStatus.PLANNED);
        originStop = stopRepository.save(originStop);

        Stop destStop = new Stop();
        destStop.setCode("STOP_DEST_" + uniqueId);
        destStop.setExternalLocationCode("DEST_BASIC");
        destStop.setStatus(StopStatus.PLANNED);
        destStop = stopRepository.save(destStop);

        Stop complexOriginStop = new Stop();
        complexOriginStop.setCode("STOP_COMPLEX_ORIGIN_" + uniqueId);
        complexOriginStop.setExternalLocationCode("ORIGIN_COMPLEX");
        complexOriginStop.setStatus(StopStatus.PLANNED);
        complexOriginStop = stopRepository.save(complexOriginStop);

        // Create Shipments with comprehensive data
        long currentTime = System.currentTimeMillis();

        // Basic Shipment 1
        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIPMENT_BASIC_1_" + uniqueId);
        shipment1.setStatus(ShipmentStatus.ASSIGNED);
        shipment1.setTrip(basicTrip);
        shipment1.setTransportOrder(basicTO);
        shipment1.setOriginStop(originStop);
        shipment1.setDestinationStop(destStop);
        shipment1.setExternalConsignmentId("CONSIGNMENT_BASIC");
        shipment1.setExternalCustomerOrderId("CO_BASIC");
        shipment1.setExpectedPickupAt(currentTime);
        shipment1.setExpectedDeliveryAt(currentTime + 3600000L);
        shipmentRepository.save(shipment1);

        // Complex Shipment 1 (with actual times)
        Shipment shipment2 = new Shipment();
        shipment2.setCode("SHIPMENT_COMPLEX_1_" + uniqueId);
        shipment2.setStatus(ShipmentStatus.ALLOCATED);
        shipment2.setTrip(complexTrip);
        shipment2.setTransportOrder(complexTO);
        shipment2.setOriginStop(complexOriginStop);
        shipment2.setDestinationStop(destStop);
        shipment2.setExternalConsignmentId("CONSIGNMENT_COMPLEX_1");
        shipment2.setExternalCustomerOrderId("CO_COMPLEX_1");
        shipment2.setExpectedPickupAt(currentTime + 1800000L);
        shipment2.setExpectedDeliveryAt(currentTime + 5400000L);
        shipment2.setActualPickupAt(currentTime + 2100000L);
        shipment2.setActualDeliveryAt(currentTime + 6000000L);
        shipmentRepository.save(shipment2);

        // Additional Shipment for testing
        Shipment shipment3 = new Shipment();
        shipment3.setCode("SHIPMENT_ADDITIONAL_" + uniqueId);
        shipment3.setStatus(ShipmentStatus.IN_TRANSIT);
        shipment3.setTrip(basicTrip);
        shipment3.setTransportOrder(basicTO);
        shipment3.setOriginStop(originStop);
        shipment3.setDestinationStop(destStop);
        shipment3.setExternalConsignmentId("CONSIGNMENT_ADDITIONAL");
        shipment3.setExternalCustomerOrderId("CO_ADDITIONAL");
        shipment3.setExpectedPickupAt(currentTime + 3600000L);
        shipment3.setExpectedDeliveryAt(currentTime + 7200000L);
        shipmentRepository.save(shipment3);
    }

    @Test
    void getShipmentView_WhenShipmentDoesNotExist_ShouldReturnNotFound() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", "NON_EXISTENT_SHIPMENT")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR"))
                .andExpect(status().isUnprocessableEntity())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").value("INVALID_REQUEST"))
                .andExpect(jsonPath("$.message").value("Shipment not found with code: NON_EXISTENT_SHIPMENT"));
    }

    // Helper methods
    private MvcResult performGetShipmentViewRequest(String shipmentCode) throws Exception {
        return mockMvc.perform(post("/v1/shipments/{shipmentCode}/view", shipmentCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
    }

    private ShipmentViewResponse parseResponse(MvcResult result) throws Exception {
        String content = result.getResponse().getContentAsString();
        return objectMapper.readValue(content, ShipmentViewResponse.class);
    }

}
