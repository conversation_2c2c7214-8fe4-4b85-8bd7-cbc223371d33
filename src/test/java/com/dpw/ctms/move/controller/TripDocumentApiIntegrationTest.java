package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.response.TripDocumentDownloadResponse;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.util.*;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class TripDocumentApiIntegrationTest extends IntegrationTestBase {

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @MockBean
    private DocumentService documentService;

    @MockBean
    private DocumentGeneratorFactory documentGeneratorFactory;

    @Autowired
    private CanonicalChecksum canonicalChecksum;

    private String tripCode;
    private Trip trip;
    private String testJson;
    private String testChecksum;

    @BeforeEach
    void setUp() {
        
        // Create a test trip
        tripCode = "TRIP_" + UUID.randomUUID().toString().substring(0, 8);
        trip = new Trip();
        trip.setCode(tripCode);
        trip.setStatus(TripStatus.CREATED);
        trip = tripRepository.save(trip);
        
        // Create test JSON and checksum
        testJson = "{\"tripCode\":\"" + tripCode + "\",\"test\":\"data\"}";
        testChecksum = canonicalChecksum.generateChecksum(testJson);
    }

    @AfterEach
    void tearDown() {
        documentRepository.deleteAll();
        tripRepository.deleteAll();
        TestDatabaseManager.cleanupCfrSchema();
    }

    @Test
    void shouldGenerateNewBolDocumentWhenNoExistingDocument() throws Exception {
        // Given
        String fileIdentifier = UUID.randomUUID().toString();
        String presignedUrl = "https://example.com/presigned-url";

        mockDocumentGeneratorFactory(testJson);
        mockDocumentService(fileIdentifier, presignedUrl);

        // When
        MvcResult result = mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        TripDocumentDownloadResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<TripDocumentDownloadResponse>() {}
        );

        assertNotNull(response);
        assertEquals(presignedUrl, response.getPresignedDownloadUrl());

        // Verify document was saved to database
        Optional<Document> savedDocument = documentRepository.findByTripCodeAndDocumentTypeAndChecksum(
                tripCode, DocumentType.BOL, testChecksum
        );
        assertTrue(savedDocument.isPresent());
        assertEquals(fileIdentifier, savedDocument.get().getFileIdentifier());
        assertEquals(tripCode, savedDocument.get().getTripCode());
        assertEquals(DocumentType.BOL, savedDocument.get().getDocumentType());

        // Verify service calls
        verify(documentService).getBol(any(PrintBolRequest.class), anyString());
        verifyNoMoreInteractions(documentService);
    }

    @Test
    void shouldGetPresignedUrlForExistingDocument() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();
        String newPresignedUrl = "https://example.com/new-presigned-url";

        // Save existing document
        Document existingDocument = Document.builder()
                .tripCode(tripCode)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJson);
        mockGetPresignedUrlService(existingFileIdentifier, newPresignedUrl);

        // When
        MvcResult result = mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // Then
        TripDocumentDownloadResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(),
                new TypeReference<TripDocumentDownloadResponse>() {}
        );

        assertNotNull(response);
        assertEquals(newPresignedUrl, response.getPresignedDownloadUrl());

        // Verify getBol was not called for existing document
        verify(documentService, never()).getBol(any(PrintBolRequest.class), anyString());
        // Verify getDownloadPreSignedURL was called
        verify(documentService).getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class));
    }

    @Test
    void shouldHandleDocumentServiceError() throws Exception {
        // Given
        mockDocumentGeneratorFactory(testJson);
        when(documentService.getBol(any(PrintBolRequest.class), anyString()))
                .thenThrow(new RuntimeException("Document service error"));

        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleInvalidTripCode() throws Exception {
        // Given
        String invalidTripCode = "INVALID_TRIP";
        mockDocumentGeneratorFactory(testJson);

        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", invalidTripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleMissingTenantHeader() throws Exception {
        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());
    }

    @Test
    void shouldHandlePresignedUrlServiceError() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();

        // Save existing document
        Document existingDocument = Document.builder()
                .tripCode(tripCode)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJson);
        when(documentService.getDownloadPreSignedURL(any(GetDownloadPreSignedURLRequest.class)))
                .thenThrow(new RuntimeException("Presigned URL service error"));

        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    @Test
    void shouldHandleEmptyFileIdentifierList() throws Exception {
        // Given
        String existingFileIdentifier = UUID.randomUUID().toString();

        // Save existing document
        Document existingDocument = Document.builder()
                .tripCode(tripCode)
                .documentType(DocumentType.BOL)
                .checksum(testChecksum)
                .fileIdentifier(existingFileIdentifier)
                .build();
        documentRepository.save(existingDocument);

        mockDocumentGeneratorFactory(testJson);

        // Mock empty response
        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> emptyResponse = 
                new DocumentServiceResponse<>();
        emptyResponse.setData(Collections.emptyList());
        when(documentService.getDownloadPreSignedURL(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(emptyResponse);

        // When & Then
        mockMvc.perform(post("/v1/trips/{tripCode}/download/bol", tripCode)
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError());
    }

    private void mockDocumentGeneratorFactory(String json) {
        var mockGenerator = mock(com.dpw.ctms.move.service.document.DocumentGenerator.class);
        when(mockGenerator.generateJson(eq(tripCode), eq(Vendor.CFR))).thenReturn(json);
        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(mockGenerator);
    }

    private void mockDocumentService(String fileIdentifier, String presignedUrl) {
        PrintBolResponse printBolResponse = new PrintBolResponse();
        printBolResponse.setFileIdentifier(fileIdentifier);
        printBolResponse.setPresignedDownloadUrl(presignedUrl);

        DocumentServiceResponse<PrintBolResponse> response = new DocumentServiceResponse<>();
        response.setData(printBolResponse);

        when(documentService.getBol(any(PrintBolRequest.class), anyString()))
                .thenReturn(response);
    }

    private void mockGetPresignedUrlService(String fileIdentifier, String presignedUrl) {
        DownloadPreSignedURLResponse downloadResponse = DownloadPreSignedURLResponse.builder()
                .fileIdentifier(fileIdentifier)
                .preSignedUrl(presignedUrl)
                .build();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = 
                new DocumentServiceResponse<>();
        response.setData(Collections.singletonList(downloadResponse));

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(response);
    }
}