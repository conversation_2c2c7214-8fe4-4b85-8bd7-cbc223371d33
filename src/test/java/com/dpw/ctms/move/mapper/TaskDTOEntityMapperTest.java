package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.dto.TaskDTO;
import com.dpw.ctms.move.dto.TaskParamDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.registry.EntityTaskMappingStrategyRegistry;
import com.dpw.ctms.move.strategy.entitytaskmapping.IEntityTaskMappingStrategy;
import com.dpw.ctms.move.util.EntityFinderUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskDTOEntityMapperTest {

    @Mock
    private EntityFinderUtil entityFinderUtil;

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private EntityTaskMappingStrategyRegistry strategyRegistry;

    @Mock
    private IEntityTaskMappingStrategy strategy;

    private TaskDTOEntityMapper taskDTOEntityMapper;

    private final TransportOrderDTO transportOrderDTO = new TransportOrderDTO();
    private final TransportOrder transportOrder = new TransportOrder();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        taskDTOEntityMapper = new TaskDTOEntityMapper(entityFinderUtil, taskMapper, strategyRegistry);
    }

    @Test
    void test_mapRelationships_withValidData_shouldMapSuccessfully() {
        // Setup DTO
        ParamValueStopDTO paramValue = new ParamValueStopDTO();
        paramValue.setExternalLocationCode("ABC");
        paramValue.setSequence(1);
        paramValue.setTaskEvent("DEPARTURE");
        TaskParamDTO taskParamDTO = new TaskParamDTO(
                TaskParamType.STOP,
                paramValue
        );


        TaskDTO taskDTO = TaskDTO.builder()
                .code("TASK1")
                .taskParams(List.of(taskParamDTO))
                .build();

        TripDTO tripDTO = TripDTO.builder()
                .code("TRIP1")
                .tasks(List.of(taskDTO))
                .build();

        transportOrderDTO.setTrips(List.of(tripDTO));

        // Setup entities
        TaskParam taskParam = new TaskParam();
        Task task = new Task();
        task.setTaskParams(List.of(taskParam));

        Trip trip = new Trip();
        trip.setCode("TRIP1");

        transportOrder.setTrips(Set.of(trip));

        // Mock behavior
        when(entityFinderUtil.getEntityByCode(any(), eq("TRIP1"), any(), any()))
                .thenReturn(trip);

        when(taskMapper.toEntity(taskDTO)).thenReturn(task);

        when(strategyRegistry.getEntityTaskMappingStrategy(TaskParamType.STOP)).thenReturn(strategy);

        // Execute
        taskDTOEntityMapper.mapRelationships(transportOrderDTO, transportOrder);

        // Verify
        verify(entityFinderUtil).getEntityByCode(any(), eq("TRIP1"), any(), any());
        verify(taskMapper).toEntity(taskDTO);
        verify(strategy).mapEntityTask(eq(paramValue), eq(trip), eq(task));
        Assertions.assertEquals(task, task.getTaskParams().get(0).getTask());
    }

    @Test
    void test_mapRelationships_whenTripsIsNull_shouldDoNothing() {
        transportOrderDTO.setTrips(null);

        taskDTOEntityMapper.mapRelationships(transportOrderDTO, transportOrder);

        verifyNoInteractions(entityFinderUtil, taskMapper, strategyRegistry);
    }

    @Test
    void test_mapRelationships_whenTaskParamsIsNull_shouldSkipStrategyMapping() {
        TaskDTO taskDTO = TaskDTO.builder().code("TASK1").taskParams(null).build();
        TripDTO tripDTO = TripDTO.builder().code("TRIP1").tasks(List.of(taskDTO)).build();
        transportOrderDTO.setTrips(List.of(tripDTO));

        Trip trip = new Trip();
        trip.setCode("TRIP1");

        Task task = new Task();
        task.setTaskParams(List.of());

        transportOrder.setTrips(Set.of(trip));

        when(entityFinderUtil.getEntityByCode(any(), eq("TRIP1"), any(), any()))
                .thenReturn(trip);
        when(taskMapper.toEntity(taskDTO)).thenReturn(task);

        taskDTOEntityMapper.mapRelationships(transportOrderDTO, transportOrder);

        verify(strategyRegistry, never()).getEntityTaskMappingStrategy(any());
    }
}



