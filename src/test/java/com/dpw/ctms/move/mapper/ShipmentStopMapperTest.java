package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ShipmentDTO;
import com.dpw.ctms.move.dto.StopDetailsDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.util.EntityFinderUtil;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShipmentStopMapperTest {

    @Mock
    private EntityFinderUtil entityFinderUtil;

    @InjectMocks
    private ShipmentStopMapper shipmentStopMapper;

    @Test
    void testMapRelationships_success() {
        // Given
        String tripCode = "TRIP001";
        String shipmentCode = "SHIP001";
        String externalLocationCode = "LOC001";

        Stop stop = new Stop();
        stop.setExternalLocationCode(externalLocationCode);
        stop.setSequence(1);
        stop.setArrivalShipments(new ArrayList<>());
        stop.setDepartureShipments(new ArrayList<>());

        Shipment shipment = new Shipment();
        shipment.setCode(shipmentCode);

        Trip trip = new Trip();
        trip.setCode(tripCode);
        trip.setShipments(new HashSet<>(List.of(shipment)));
        trip.setStops(new HashSet<>(List.of(stop)));

        TripDTO tripDTO = new TripDTO();
        tripDTO.setCode(tripCode);

        ShipmentDTO shipmentDTO = new ShipmentDTO();
        shipmentDTO.setCode(shipmentCode);

        StopDetailsDTO origin = new StopDetailsDTO();
        origin.setExternalLocationCode(externalLocationCode);
        origin.setSequence(1);

        StopDetailsDTO destination = new StopDetailsDTO();
        destination.setExternalLocationCode(externalLocationCode);
        destination.setSequence(1);

        shipmentDTO.setOriginStop(origin);
        shipmentDTO.setDestinationStop(destination);
        tripDTO.setShipments(List.of(shipmentDTO));

        TransportOrderDTO dto = new TransportOrderDTO();
        dto.setTrips(List.of(tripDTO));

        TransportOrder order = new TransportOrder();
        order.setTrips(Set.of(trip));

        // When
        when(entityFinderUtil.getEntityByCode(eq(order.getTrips().stream().toList()), eq(tripCode), any(), any()))
                .thenReturn(trip);
        when(entityFinderUtil.getEntityByCode(eq(trip.getShipments().stream().toList()), eq(shipmentCode), any(), any()))
                .thenReturn(shipment);

        shipmentStopMapper.mapRelationships(dto, order);

        // Then
        Assertions.assertEquals(stop, shipment.getOriginStop());
        Assertions.assertEquals(stop, shipment.getDestinationStop());
        Assertions.assertTrue(stop.getDepartureShipments().contains(shipment));
        Assertions.assertTrue(stop.getArrivalShipments().contains(shipment));
    }

    @Test
    void testMapRelationships_tripNotFound_throwsException() {
        // Given
        String tripCode = "TRIP001";
        TripDTO tripDTO = new TripDTO();
        tripDTO.setCode(tripCode);
        tripDTO.setShipments(List.of());  // Empty but not null

        TransportOrderDTO dto = new TransportOrderDTO();
        dto.setTrips(List.of(tripDTO));

        TransportOrder order = new TransportOrder();
        order.setTrips(Set.of());

        when(entityFinderUtil.getEntityByCode(any(), eq(tripCode), any(), any()))
                .thenThrow(new TMSException(INVALID_REQUEST.name(), "Trip not found"));

        // Expect
        assertThrows(TMSException.class, () -> shipmentStopMapper.mapRelationships(dto, order));
    }

    @Test
    void testMapRelationships_shipmentNotFound_throwsException() {
        // Given
        String tripCode = "TRIP001";
        String shipmentCode = "SHIP001";

        TripDTO tripDTO = new TripDTO();
        tripDTO.setCode(tripCode);

        ShipmentDTO shipmentDTO = new ShipmentDTO();
        shipmentDTO.setCode(shipmentCode);

        tripDTO.setShipments(List.of(shipmentDTO));

        TransportOrderDTO dto = new TransportOrderDTO();
        dto.setTrips(List.of(tripDTO));

        Trip trip = new Trip();
        trip.setCode(tripCode);
        trip.setShipments(new HashSet<>(List.of()));

        TransportOrder order = new TransportOrder();
        order.setTrips(Set.of(trip));

        when(entityFinderUtil.getEntityByCode(eq(order.getTrips().stream().toList()), eq(tripCode), any(), any()))
                .thenReturn(trip);
        when(entityFinderUtil.getEntityByCode(eq(trip.getShipments().stream().toList()), eq(shipmentCode), any(), any()))
                .thenThrow(new TMSException(INVALID_REQUEST.name(), "Shipment not found"));

        // Expect
        assertThrows(TMSException.class, () -> shipmentStopMapper.mapRelationships(dto, order));
    }
}

