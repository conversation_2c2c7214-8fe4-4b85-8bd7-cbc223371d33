package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class TaskInstanceRegistrationRequestMapperTest {

    private final TaskInstanceRegistrationRequestMapper mapper = Mappers.getMapper(TaskInstanceRegistrationRequestMapper.class);

    @Test
    void testToTaskInstanceRegisterRequest() {
        Task task = Task.builder()
                .code("TASK123")
                .externalTaskMasterCode("MASTER123")
                .expectedStartAt(1000L)
                .expectedEndAt(2000L)
                .build();
        List<Task> tasks = Collections.singletonList(task);

        TaskInstanceRegistrationRequest request = mapper.toTaskInstanceRegisterRequest(tasks);
        assertNotNull(request);
        assertNotNull(request.getTasks());
        assertEquals(1, request.getTasks().size());
        TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest details = request.getTasks().get(0);
        assertEquals("TASK123", details.getExtTaskTransactionCode());
        assertEquals("MASTER123", details.getExtTaskMasterCode());
        assertEquals(TaskServiceConstants.DEFAULT_TENANT_CODE, details.getTenantCode());
        assertEquals(TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE, details.getTenantServiceCode());
        assertNotNull(details.getExpectedDateRange());
        assertEquals(1000L, details.getExpectedDateRange().getStart());
        assertEquals(2000L, details.getExpectedDateRange().getEnd());
    }
}

