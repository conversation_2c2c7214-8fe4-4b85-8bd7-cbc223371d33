package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class ShipmentViewMapperTest {

    private ShipmentViewMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(ShipmentViewMapper.class);
    }

    @Test
    void toResponse_WithCompleteShipment_ShouldMapAllFields() {
        // Given
        Shipment shipment = createCompleteShipment();

        // When
        ShipmentViewResponse response = mapper.toResponse(shipment);

        // Then
        assertNotNull(response);
        assertEquals("SHIP-001", response.getShipmentCode());
        assertEquals(ShipmentStatus.ASSIGNED.name(), response.getStatus().getValue());
        assertEquals(ShipmentStatus.ASSIGNED.getDisplayName(), response.getStatus().getLabel());
        
        // Verify origin stop
        assertNotNull(response.getOriginStop());
        assertEquals("ORIGIN-001", response.getOriginStop().getCode());
        assertEquals("LOC-ORIGIN", response.getOriginStop().getExternalLocationCode());
        assertEquals(StopStatus.PLANNED.name(), response.getOriginStop().getStatus().getValue());
        
        // Verify destination stop
        assertNotNull(response.getDestinationStop());
        assertEquals("DEST-001", response.getDestinationStop().getCode());
        assertEquals("LOC-DEST", response.getDestinationStop().getExternalLocationCode());
        
        // Verify customer order
        assertNotNull(response.getCustomerOrder());
        assertEquals("CO-001", response.getCustomerOrder().getCustomerOrderId());
        assertEquals("CONSIGN-001", response.getCustomerOrder().getConsignmentId());
        
        // Verify transport order
        assertNotNull(response.getTransportOrder());
        assertEquals("TO-001", response.getTransportOrder().getTransportOrderCode());
        
        // Verify expected times
        assertNotNull(response.getExpectedTimes());
        assertEquals(1000L, response.getExpectedTimes().getStartAt());
        assertEquals(2000L, response.getExpectedTimes().getEndAt());
        
        // Verify actual times
        assertNotNull(response.getActualTimes());
        assertEquals(1500L, response.getActualTimes().getStartAt());
        assertEquals(2500L, response.getActualTimes().getEndAt());
        
        // Verify volume and weight
        assertEquals(BigDecimal.valueOf(100.5), response.getVolume());
        assertEquals("L", response.getVolumeUom());
        assertEquals(BigDecimal.valueOf(50.25), response.getWeight());
        assertEquals("KG", response.getWeightUom());
    }

    @Test
    void toResponse_WithNullShipment_ShouldReturnNull() {
        // When
        ShipmentViewResponse response = mapper.toResponse(null);

        // Then
        assertNull(response);
    }

    @Test
    void toResponse_WithMinimalShipment_ShouldHandleNullFields() {
        // Given
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP-MINIMAL");
        shipment.setStatus(ShipmentStatus.IN_TRANSIT);
        shipment.setExternalConsignmentId("CONSIGN-MIN");
        shipment.setExternalCustomerOrderId("CO-MIN");

        // When
        ShipmentViewResponse response = mapper.toResponse(shipment);

        // Then
        assertNotNull(response);
        assertEquals("SHIP-MINIMAL", response.getShipmentCode());
        assertEquals(ShipmentStatus.IN_TRANSIT.name(), response.getStatus().getValue());
        assertEquals(ShipmentStatus.IN_TRANSIT.getDisplayName(), response.getStatus().getLabel());
        
        // Verify null fields are handled correctly
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
        assertNull(response.getTransportOrder());
        
        // Customer order should still be mapped
        assertNotNull(response.getCustomerOrder());
        assertEquals("CO-MIN", response.getCustomerOrder().getCustomerOrderId());
        assertEquals("CONSIGN-MIN", response.getCustomerOrder().getConsignmentId());
        
        // Time ranges should be created but with null values
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }

    @Test
    void mapStatusInfo_WithDisplayableStatusEnum_ShouldMapCorrectly() {
        // When
        EnumLabelValueResponse result = mapper.mapStatusInfo(ShipmentStatus.DELIVERED);

        // Then
        assertNotNull(result);
        assertEquals(ShipmentStatus.DELIVERED.getDisplayName(), result.getLabel());
        assertEquals(ShipmentStatus.DELIVERED.name(), result.getValue());
    }

    @Test
    void mapStatusInfo_WithNullStatus_ShouldReturnNull() {
        // When
        EnumLabelValueResponse result = mapper.mapStatusInfo(null);

        // Then
        assertNull(result);
    }

    @Test
    void mapStop_WithCompleteStop_ShouldMapAllFields() {
        // Given
        Stop stop = new Stop();
        stop.setCode("STOP-001");
        stop.setExternalLocationCode("LOC-001");
        stop.setStatus(StopStatus.COMPLETED);

        // When
        ShipmentViewResponse.Stop result = mapper.mapStop(stop);

        // Then
        assertNotNull(result);
        assertEquals("STOP-001", result.getCode());
        assertEquals("LOC-001", result.getExternalLocationCode());
        assertNotNull(result.getStatus());
        assertEquals(StopStatus.COMPLETED.name(), result.getStatus().getValue());
        assertEquals(StopStatus.COMPLETED.getDisplayName(), result.getStatus().getLabel());
    }

    @Test
    void mapStop_WithNullStop_ShouldReturnNull() {
        // When
        ShipmentViewResponse.Stop result = mapper.mapStop(null);

        // Then
        assertNull(result);
    }

    @Test
    void mapCustomerOrder_ShouldMapFromShipmentFields() {
        // Given
        Shipment shipment = new Shipment();
        shipment.setExternalCustomerOrderId("CO-123");
        shipment.setExternalConsignmentId("CONSIGN-123");

        // When
        ShipmentViewResponse.CustomerOrder result = mapper.mapCustomerOrder(shipment);

        // Then
        assertNotNull(result);
        assertEquals("CO-123", result.getCustomerOrderId());
        assertEquals("CONSIGN-123", result.getConsignmentId());
    }

    @Test
    void mapTransportOrder_WithCompleteTransportOrder_ShouldMapCode() {
        // Given
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO-123");

        // When
        ShipmentViewResponse.TransportOrder result = mapper.mapTransportOrder(transportOrder);

        // Then
        assertNotNull(result);
        assertEquals("TO-123", result.getTransportOrderCode());
    }

    @Test
    void mapTransportOrder_WithNullTransportOrder_ShouldReturnNull() {
        // When
        ShipmentViewResponse.TransportOrder result = mapper.mapTransportOrder(null);

        // Then
        assertNull(result);
    }

    @Test
    void mapExpectedTimes_ShouldMapFromShipmentTimeFields() {
        // Given
        Shipment shipment = new Shipment();
        shipment.setExpectedPickupAt(1000L);
        shipment.setExpectedDeliveryAt(2000L);

        // When
        ShipmentViewResponse.TimeRange result = mapper.mapExpectedTimes(shipment);

        // Then
        assertNotNull(result);
        assertEquals(1000L, result.getStartAt());
        assertEquals(2000L, result.getEndAt());
    }

    @Test
    void mapActualTimes_ShouldMapFromShipmentActualTimeFields() {
        // Given
        Shipment shipment = new Shipment();
        shipment.setActualPickupAt(1500L);
        shipment.setActualDeliveryAt(2500L);

        // When
        ShipmentViewResponse.TimeRange result = mapper.mapActualTimes(shipment);

        // Then
        assertNotNull(result);
        assertEquals(1500L, result.getStartAt());
        assertEquals(2500L, result.getEndAt());
    }

    @Test
    void mapActualTimes_WithNullActualTimes_ShouldReturnTimeRangeWithNulls() {
        // Given
        Shipment shipment = new Shipment();
        // actualPickupAt and actualDeliveryAt are null by default

        // When
        ShipmentViewResponse.TimeRange result = mapper.mapActualTimes(shipment);

        // Then
        assertNotNull(result);
        assertNull(result.getStartAt());
        assertNull(result.getEndAt());
    }

    private Shipment createCompleteShipment() {
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP-001");
        shipment.setStatus(ShipmentStatus.ASSIGNED);
        shipment.setExternalConsignmentId("CONSIGN-001");
        shipment.setExternalCustomerOrderId("CO-001");
        shipment.setExpectedPickupAt(1000L);
        shipment.setExpectedDeliveryAt(2000L);
        shipment.setActualPickupAt(1500L);
        shipment.setActualDeliveryAt(2500L);
        shipment.setVolume(BigDecimal.valueOf(100.5));
        shipment.setVolumeUom("L");
        shipment.setWeight(BigDecimal.valueOf(50.25));
        shipment.setWeightUom("KG");

        // Create origin stop
        Stop originStop = new Stop();
        originStop.setCode("ORIGIN-001");
        originStop.setExternalLocationCode("LOC-ORIGIN");
        originStop.setStatus(StopStatus.PLANNED);
        shipment.setOriginStop(originStop);

        // Create destination stop
        Stop destinationStop = new Stop();
        destinationStop.setCode("DEST-001");
        destinationStop.setExternalLocationCode("LOC-DEST");
        destinationStop.setStatus(StopStatus.PLANNED);
        shipment.setDestinationStop(destinationStop);

        // Create transport order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO-001");
        transportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        shipment.setTransportOrder(transportOrder);

        return shipment;
    }
}
