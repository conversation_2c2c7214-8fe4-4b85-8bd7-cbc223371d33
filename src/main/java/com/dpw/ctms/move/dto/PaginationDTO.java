package com.dpw.ctms.move.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaginationDTO {
    private Integer pageNo;
    private Integer pageSize;
    
    // Default values
    private static final int DEFAULT_PAGE_NO = 0;
    private static final int DEFAULT_PAGE_SIZE = 10;
    
    // Helper method to get pageNo with default
    public Integer getPageNoOrDefault() {
        return pageNo != null ? pageNo : DEFAULT_PAGE_NO;
    }
    
    // Helper method to get pageSize with default
    public Integer getPageSizeOrDefault() {
        return pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
    }
}