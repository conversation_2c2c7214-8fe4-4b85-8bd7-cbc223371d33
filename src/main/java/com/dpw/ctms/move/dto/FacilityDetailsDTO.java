package com.dpw.ctms.move.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class FacilityDetailsDTO {
    private Long id;
    private String code;
    private String name;
    private String ownershipType;
    private String instructions;
    private PointOfContactDetails pointOfContactDetails;
    private AddressDetails addressDetails;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PointOfContactDetails {
        private String firstName;
        private String lastName;
        private String emailId;
        private String phoneNumber;
        private String countryCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class AddressDetails {
        private String addressLine;
        private String postalCode;
        private String suburbName;
        private String cityName;
        private String provinceName;
        private String countryName;
    }
}