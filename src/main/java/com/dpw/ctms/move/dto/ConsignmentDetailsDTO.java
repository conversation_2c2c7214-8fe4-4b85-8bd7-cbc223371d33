package com.dpw.ctms.move.dto;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class ConsignmentDetailsDTO {
    private String consignmentId;
    private String consignmentCode;
    private Long lineItemId;
    private String lineItemCode;
    private Long customerOrderId;
    private String customerOrderCode;
    private CustomerOrderMetaDataDTO customerOrderMetaData;
    private String specialInstructions;
    private String customerId;
    private String customerName;
    private List<String> shipmentIds;
    private ProductDetailsDTO productDetailsDTO;
    private String originFacilityId;
    private String destinationFacilityId;
    private DateTimeDTO expectedPickupTime;
    private DateTimeDTO expectedDeliveryTime;
    private DateTimeDTO actualPickupTime;
    private DateTimeDTO actualDeliveryTime;
    private String status;
    private String movementType;
}
