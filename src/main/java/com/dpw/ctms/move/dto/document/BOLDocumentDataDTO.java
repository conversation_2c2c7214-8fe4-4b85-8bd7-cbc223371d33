package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;


//TODO: add vehicle details and trailer details if needed
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class BOLDocumentDataDTO {
    private List<String> externalConsignmentIds;
    private List<Long> shipmentIds;
    private Map<String, ConsignmentDetailsDTO> consignmentDetailsMap;
    private Map<String,FacilityDetailsDTO> facilityDetailsMap;
    private List<String> externalFacilityCodes;
    private TripDocumentDTO tripDetails;
}
