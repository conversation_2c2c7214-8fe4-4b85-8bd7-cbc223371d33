package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Vendor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * Generic document generation context that holds common data and document-specific data
 * @param <T> Document-specific data type
 */
@SuperBuilder
@Data
public class DocumentGenerationContextDTO<T> {
    private Vendor vendor;
    private DocumentType documentType;
    private T documentData;
}
