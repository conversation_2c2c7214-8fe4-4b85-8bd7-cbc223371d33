package com.dpw.ctms.move.dto.consumer.cfr;

import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@Data
@SuperBuilder
public class PickupTaskMessageDTO extends IntegratorMessageRequestDTO<PickupTaskMessageDTO.PercolatedRecordDTO> {

    @AllArgsConstructor
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class PercolatedRecordDTO {
        private Long arrivalTime;
        private Long loadingCompletionTime;
        private Long departureTime;
    }
}