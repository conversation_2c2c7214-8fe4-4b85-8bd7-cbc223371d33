package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDTO {
    private String tripCode;
    private DocumentType documentType;
    private String checksum;
    private String fileIdentifier;
}

