package com.dpw.ctms.move.dto;

import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VehicleOperatorResourceDTO {
    private String code;
    private String externalResourceId;
    private ResourceAssignmentDetailsDTO resourceAssignmentDetails;
    private List<AssignedStopRangeDTO> assignedStopRanges;
    private JsonNode details;
}
