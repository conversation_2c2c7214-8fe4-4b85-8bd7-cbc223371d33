package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.enums.TripStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO containing trip details required for document generation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TripDocumentDTO {
    private String tripCode;
    private TripStatus status;
    private String externalOriginLocationCode;
    private String externalDestinationLocationCode;
    private DateTimeDTO expectedStartAt;
    private DateTimeDTO expectedEndAt;
    private DateTimeDTO actualStartAt;
    private DateTimeDTO actualEndAt;
}