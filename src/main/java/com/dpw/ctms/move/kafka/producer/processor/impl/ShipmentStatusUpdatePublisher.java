package com.dpw.ctms.move.kafka.producer.processor.impl;

import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.message.ShipmentStatusUpdateMessage;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.dpw.ctms.move.enums.MessageActionType.SHIPMENT_STATUS_UPDATE;


@Component
@RequiredArgsConstructor
@Slf4j
@LogExecutionTime
public class ShipmentStatusUpdatePublisher implements IEventRequestPublisher<ShipmentStatusUpdateMessage> {
    private final IEventProcessorService<ShipmentStatusUpdateMessage> eventProcessorService;
    private final KafkaProducer kafkaProducer;

    @PostConstruct
    public void init() {
        eventProcessorService.addRequestProcessor(SHIPMENT_STATUS_UPDATE.name(),
                this);
    }

    @Override
    public boolean process(IntegratorMessageRequest<ShipmentStatusUpdateMessage> request) {
        log.info("Inside ShipmentStatusUpdatePublisher processing message");
        boolean isEventPublished = kafkaProducer.publishEvent(request);
        if (isEventPublished) {
            log.info("Message sent on topic: {}", request.getTransactionContext().getTopic());
        }
        return isEventPublished;
    }
}
