package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskLifecycleEvent;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.enums.TripLifecycleEvent;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class TaskPercolationHelperUtil {

    private final StateMachineServiceRegistry stateMachineServiceRegistry;
    private final ITaskService taskService;

    public void updateTripStatus(Trip trip) {

        stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.START_TRIP.name(), trip.getId());

        Set<Task> tripTasks = trip.getStops().stream()
                .flatMap(stop -> stop.getStopTasks().stream().map(StopTask::getTask))
                .collect(Collectors.toSet());

        // Update trip to completed if all tasks are completed or closed
        // even if all tasks are closed it will move to closed in next step
        boolean tripTasksCompleted = tripTasks.stream()
                .filter(task -> task.getStatus() != TaskStatus.DISCARDED)
                .allMatch(task -> task.getStatus() == TaskStatus.COMPLETED
                        || task.getStatus() == TaskStatus.CLOSED);
        if (tripTasksCompleted)
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                    .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.END_TRIP.name(), trip.getId());

        // Update trip to closed if all tasks are closed
        boolean tripTasksClosed = tripTasks.stream()
                .filter(task -> task.getStatus() != TaskStatus.DISCARDED)
                .allMatch(task -> task.getStatus() == TaskStatus.CLOSED);
        if (tripTasksClosed)
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                    .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.CLOSE_TRIP.name(), trip.getId());
    }

    @Transactional
    public void updateTask(Task task, TaskStatus taskStatus,
                           IntegratorMessageRequestDTO<?> message) {
        task.setActualEndAt(message.getTransactionContext().getDateTime());
        if (taskStatus.equals(TaskStatus.CLOSED)) {
            task.setDetails(ObjectMapperUtil.getObjectMapper()
                    .convertValue(message.getMessage().getPercolatedRecords(), JsonNode.class));
        }
        taskService.saveTask(task);
        TaskLifecycleEvent taskLifecycleEvent = (taskStatus.equals(TaskStatus.CLOSED) ?
                TaskLifecycleEvent.TASK_CLOSED : TaskLifecycleEvent.TASK_COMPLETED);
        stateMachineServiceRegistry.getService(StateMachineEntityType.TASK)
                .handleEvent(TenantContext.getCurrentTenant(), taskLifecycleEvent.name(), task.getId());
    }

    public ParamValueShipmentDTO extractShipmentDTOFromTask(Task task, String taskCode) {
        ParamValueShipmentDTO shipmentDTO = task.getTaskParams().stream()
                .filter(t -> StringUtils.equalsAnyIgnoreCase(t.getParamName().name(), ("SHIPMENT")))
                .map(TaskParam::getParamValue)
                .map(val -> ObjectMapperUtil.getObjectMapper().convertValue(val, ParamValueShipmentDTO.class))
                .findFirst()
                .orElse(null);

        if (shipmentDTO == null) {
            log.error("No shipment found for task code {}", taskCode);
            throw new com.dpw.tmsutils.exception.TMSException(
                    com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format("No shipment found for task code %s", taskCode));
        }
        return shipmentDTO;
    }
}
