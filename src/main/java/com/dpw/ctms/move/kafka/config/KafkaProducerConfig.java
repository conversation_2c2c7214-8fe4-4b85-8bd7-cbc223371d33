package com.dpw.ctms.move.kafka.config;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.InitializingBean;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "kafka.producer")
@EnableConfigurationProperties(KafkaProducerConfig.class)
public class KafkaProducerConfig implements InitializingBean {

    @Value("${kafka.security.protocol:SASL_SSL}")
    private String securityProtocol;

    @Value("${kafka.sasl.mechanism:PLAIN}")
    private String saslMechanism;

    @Value("${kafka.producer.request-timeout-ms:30000}")
    private String requestTimeoutMs;
    
    @Value("${kafka.producer.metadata-max-age:180000}")
    private String metadataMaxAge;
    
    @Value("${kafka.producer.metadata-max-idle:180000}")
    private String metadataMaxIdle;
    
    @Value("${kafka.producer.connections-max-idle-ms:180000}")
    private String connectionsMaxIdleMs;

    @Setter
    private Map<String, Map<String, String>> topics = new HashMap<>();
    
    private final Map<String, KafkaTemplate<String, Object>> kafkaTemplateCache = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        initializeKafkaTemplates();
    }

    public void initializeKafkaTemplates() {
        if (topics != null && !topics.isEmpty()) {
            log.info("Found {} topic configurations", topics.size());
            for (Map.Entry<String, Map<String, String>> topicEntry : topics.entrySet()) {
                Map<String, String> topicConfig = topicEntry.getValue();
                String actualTopicName = topicConfig.get("name");
                log.info("Creating KafkaTemplate for topic: {} with config: {}", actualTopicName, topicConfig);
                createKafkaTemplate(actualTopicName, topicConfig);
            }
        } else {
            log.error("No topic configurations found! Topics map is: {}", topics);
        }
        log.info("Kafka templates initialization completed. Total templates: {}", kafkaTemplateCache.size());
    }

    private void createKafkaTemplate(String topicName, Map<String, String> topicConfig) {
        String bootstrapServers = topicConfig.get("bootstrap-servers");
        String username = topicConfig.get("username");
        String password = topicConfig.get("password");
        
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, requestTimeoutMs);
        configProps.put(ProducerConfig.METADATA_MAX_AGE_CONFIG, metadataMaxAge);
        configProps.put(ProducerConfig.METADATA_MAX_IDLE_CONFIG, metadataMaxIdle);
        configProps.put(ProducerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, connectionsMaxIdleMs);
        configProps.put("security.protocol", securityProtocol);
        configProps.put("sasl.mechanism", saslMechanism);
        configProps.put("sasl.jaas.config", String.format(
                "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                username, password));
        
        ProducerFactory<String, Object> producerFactory = new DefaultKafkaProducerFactory<>(configProps);
        kafkaTemplateCache.put(topicName, new KafkaTemplate<>(producerFactory));
    }

    @Bean("topicSpecificKafkaTemplateMap")
    @Primary
    public Map<String, KafkaTemplate<String, Object>> kafkaTemplateMap() {
        return kafkaTemplateCache;
    }

}
