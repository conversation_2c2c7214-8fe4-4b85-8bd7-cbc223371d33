package com.dpw.ctms.move.kafka.producer.impl;

import com.dpw.ctms.move.kafka.config.KafkaProducerConfig;
import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;


@Slf4j
@Service
public class KafkaProducerImpl implements KafkaProducer {

    @Autowired
    @Qualifier("topicSpecificKafkaTemplateMap")
    private Map<String, KafkaTemplate<String, Object>> kafkaTemplateMap;

    @Override
    public <T> boolean publishEvent(IntegratorMessageRequest<T> request) {
        String topic = request.getTransactionContext().getTopic();
        if (StringUtils.isBlank(topic)) {
            throw new TMSException(TMSExceptionErrorCode.INVALID_REQUEST.name(), "Invalid topic");
        }
        AtomicBoolean isEventPublished = new AtomicBoolean(false);
        try {
            // Get the appropriate KafkaTemplate for this topic
            log.info("Publishing event to topic: {}", topic);
            kafkaTemplateMap.forEach((key, value) -> {
                log.info("KafkaTemplate entry => key: {}, value: {}", key, value);
            });
            KafkaTemplate<String, Object> topicSpecificTemplate = kafkaTemplateMap.get(topic);
            if (topicSpecificTemplate == null) {
                log.error("No template found for topic: {}", topic);
                throw new TMSException(TMSExceptionErrorCode.INVALID_REQUEST.name(), "No template found for topic: " + topic);
            }
            
            CompletableFuture<SendResult<String, Object>> future = null;
            ProducerRecord<String, Object> record = new ProducerRecord<>(topic, request);
            future = topicSpecificTemplate.send(record);
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Sent message=[{}] with offset=[{}]", request, result.getRecordMetadata().offset());
                    isEventPublished.set(true);
                } else {
                    log.info("Unable to send message=[{}] due to : {}", request, ex.getMessage());
                }
            }).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();

            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(), e.getMessage());
        } catch (Exception e) {
            log.error("Failed to send message=[{}] due to : {}", request, e);
            return false;
        }
        return isEventPublished.get();
    }
}