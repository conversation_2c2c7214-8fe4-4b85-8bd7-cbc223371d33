package com.dpw.ctms.move.kafka.producer.processor.impl;

import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.message.TripStatusUpdateMessage;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.dpw.ctms.move.enums.MessageActionType.TASK_STATUS_UPDATE;
import static com.dpw.ctms.move.enums.MessageActionType.TRIP_STATUS_UPDATE;

@Component
@RequiredArgsConstructor
@Slf4j
@LogExecutionTime
public class TaskStatusUpdateRequestPublisher implements IEventRequestPublisher<TripStatusUpdateMessage> {
    private final IEventProcessorService<TripStatusUpdateMessage> eventProcessorService;
    private final KafkaProducer kafkaProducer;

    @PostConstruct
    public void init() {
        eventProcessorService.addRequestProcessor(TASK_STATUS_UPDATE.name(),
                this);
    }

    @Override
    public boolean process(IntegratorMessageRequest<TripStatusUpdateMessage> request) {
        log.info("Inside TaskStatusUpdateRequestPublisher processing message");
        boolean isEventPublished = kafkaProducer.publishEvent(request);
        if (isEventPublished) {
            log.info("Message sent on topic: {}", request.getTransactionContext().getTopic());
        }
        return isEventPublished;
    }
}
