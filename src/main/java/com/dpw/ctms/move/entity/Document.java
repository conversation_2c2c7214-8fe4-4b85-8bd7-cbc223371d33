package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.DocumentType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "document")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Document extends BaseEntity{

    @Column(name = "trip_code", nullable = false)
    private String tripCode;

    @Column(name = "document_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    @Column(name = "checksum")
    private String checksum;

    @Column(name = "file_identifier")
    private String fileIdentifier;

}
