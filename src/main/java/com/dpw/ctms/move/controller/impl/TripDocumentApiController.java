package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.TripDocumentApi;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.response.TripDocumentDownloadResponse;
import com.dpw.ctms.move.service.ITripDocumentService;
import com.dpw.tmsutils.threadlocal.TenantContext;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@Validated
public class TripDocumentApiController implements TripDocumentApi {

    private final ITripDocumentService tripDocumentService;

    @Override
    public ResponseEntity<TripDocumentDownloadResponse> downloadTripBolDocument(String tripCode) {

        return ResponseEntity.ok(tripDocumentService.downloadTripBolDocument(tripCode, Vendor.valueOf(TenantContext.getCurrentTenant())));
    }
}
