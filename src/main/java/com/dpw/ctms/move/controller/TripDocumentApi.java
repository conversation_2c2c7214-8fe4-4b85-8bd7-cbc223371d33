package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.response.TripDocumentDownloadResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1/trips")
public interface TripDocumentApi {

    @PostMapping(value = "/{tripCode}/download/bol", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get trip bol download response")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully download response trip bol document"),
            @ApiResponse(responseCode = "422", description = "Unable to download trip bol document", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<TripDocumentDownloadResponse> downloadTripBolDocument(
            @PathVariable("tripCode") @Parameter(name = "tripCode", required = true, description = "Trip code")
            @NotNull String tripCode);
}
