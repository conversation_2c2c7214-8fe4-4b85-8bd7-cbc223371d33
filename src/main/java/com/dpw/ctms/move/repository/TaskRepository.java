package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {
    // Additional custom query methods can be added here if needed
    Optional<Task> findByCode(String code);
}