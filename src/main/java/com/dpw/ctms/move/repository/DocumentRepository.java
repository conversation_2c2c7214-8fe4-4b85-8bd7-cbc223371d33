package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {

    Optional<Document> findByTripCodeAndDocumentTypeAndChecksum(String tripCode,
                                                           DocumentType documentType,
                                                           String checksum);

}
