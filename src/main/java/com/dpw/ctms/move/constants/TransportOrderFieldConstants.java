package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TransportOrderFieldConstants {
    // ===== BASIC TRANSPORT_ORDER ENTITY FIELDS =====
    public static final String CODE = "code";
    public static final String STATUS = "status";
    public static final String CREATED_AT = "createdAt";
    public static final String UPDATED_AT = "updatedAt";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
    public static final String ASSIGNMENT_TYPE = "assignmentType";
    public static final String ASSIGNEE_IDENTIFIER = "assigneeIdentifier";

    // ===== SHIPMENT FIELDS =====
    public static final String SHIPMENT_CODE = "code";
    public static final String EXPECTED_PICKUP_AT = "expectedPickupAt";
    public static final String EXPECTED_DELIVERY_AT = "expectedDeliveryAt";
    public static final String ACTUAL_PICKUP_AT = "actualPickupAt";
    public static final String ACTUAL_DELIVERY_AT = "actualDeliveryAt";


    // ===== TRIP FIELDS =====
    public static final String TRIP_CODE = "code";


    // ===== NESTED FIELD ACCESSORS =====
    public static final String EXTERNAL_CUSTOMER_ORDER_ID = "externalCustomerOrderId";
    public static final String EXTERNAL_CONSIGNMENT_ID = "externalConsignmentId";
    public static final String EXTERNAL_VEHICLE_TYPE_ID = "externalVehicleTypeId";
    public static final String VEHICLE_EXTERNAL_RESOURCE_ID = "externalResourceId";
    public static final String TRAILER_EXTERNAL_RESOURCE_ID = "externalResourceId";
    public static final String VEHICLE_OPERATOR_RESOURCE_ID = "externalResourceId";



    // ===== RELATIONSHIP FIELD NAMES =====
    public static final String SHIPMENTS = "shipments";
    public static final String TRIPS = "trips";

    public static final String ORIGIN_STOP = "originStop";
    public static final String DESTINATION_STOP = "destinationStop";
    public static final String EXTERNAL_LOCATION_CODE = "externalLocationCode";
    public static final String VEHICLE_RESOURCE = "vehicleResource";
    public static final String TRAILER_RESOURCES = "trailerResources";
    public static final String VEHICLE_OPERATOR_RESOURCES = "vehicleOperatorResources";


    /**
     * Inner class containing sort field mappings for API to entity field translation
     */
    public static class SortFields {
        // API field names (keys) to entity field names (values)
        public static final String API_CODE = "code";
        public static final String API_STATUS = "status";
        public static final String API_CREATED_AT = "createdAt";
        public static final String API_UPDATED_AT = "updatedAt";
        public static final String API_CREATED_BY = "createdBy";
        public static final String API_UPDATED_BY = "updatedBy";
        public static final String API_ASSIGNMENT_TYPE = "assignmentType";
    }

    // ===== DEFAULT VALUES =====
    public static final String DEFAULT_SORT_BY = CREATED_AT;
    public static final String DEFAULT_SORT_ORDER = "DESC";
}
