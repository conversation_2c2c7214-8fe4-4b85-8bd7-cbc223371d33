package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Constants class containing field names used in Trip entity queries and mappings.
 * This helps eliminate error-prone hardcoded strings and improves maintainability.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TripFieldConstants {

    // ===== BASIC TRIP ENTITY FIELDS =====
    public static final String CODE = "code";
    public static final String STATUS = "status";
    public static final String CREATED_AT = "createdAt";
    public static final String UPDATED_AT = "updatedAt";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";

    // ===== TRIP LOCATION FIELDS =====
    public static final String EXTERNAL_ORIGIN_LOCATION_CODE = "externalOriginLocationCode";
    public static final String EXTERNAL_DESTINATION_LOCATION_CODE = "externalDestinationLocationCode";

    // ===== TRIP TIMESTAMP FIELDS =====
    public static final String EXPECTED_START_AT = "expectedStartAt";
    public static final String EXPECTED_END_AT = "expectedEndAt";
    public static final String ACTUAL_START_AT = "actualStartAt";
    public static final String ACTUAL_END_AT = "actualEndAt";

    // ===== RELATIONSHIP FIELD NAMES =====
    public static final String TRANSPORT_ORDER = "transportOrder";
    public static final String SHIPMENTS = "shipments";
    public static final String STOPS = "stops";
    public static final String VEHICLE_RESOURCE = "vehicleResource";
    public static final String TRAILER_RESOURCES = "trailerResources";
    public static final String VEHICLE_OPERATOR_RESOURCES = "vehicleOperatorResources";

    // ===== TRANSPORT ORDER FIELDS =====
    public static final String TRANSPORT_ORDER_CODE = "transportOrder.code";
    public static final String TRANSPORT_ORDER_STATUS = "transportOrder.status";
    public static final String TRANSPORT_ORDER_ASSIGNMENT_TYPE = "transportOrder.assignmentType";
    public static final String TRANSPORT_ORDER_ASSIGNEE_IDENTIFIER = "transportOrder.assigneeIdentifier";

    // ===== VEHICLE RESOURCE FIELDS =====
    public static final String VEHICLE_REGISTRATION_NUMBER = "vehicleResource.registrationNumber";
    public static final String VEHICLE_EXTERNAL_TYPE_ID = "vehicleResource.externalVehicleTypeId";
    public static final String VEHICLE_EXTERNAL_RESOURCE_ID = "vehicleResource.externalResourceId";

    // ===== SHIPMENT FIELDS =====
    public static final String SHIPMENT_CODE = "code";

    // ===== NESTED FIELD ACCESSORS =====
    public static final String ASSIGNEE_IDENTIFIER = "assigneeIdentifier";
    public static final String REGISTRATION_NUMBER = "registrationNumber";
    public static final String EXTERNAL_VEHICLE_TYPE_ID = "externalVehicleTypeId";
    public static final String EXTERNAL_RESOURCE_ID = "externalResourceId";
    public static final String EXTERNAL_CUSTOMER_ORDER_ID = "externalCustomerOrderId";
    public static final String EXTERNAL_CONSIGNMENT_ID = "externalConsignmentId";

    /**
     * Inner class containing sort field mappings for API to entity field translation
     */
    public static class SortFields {
        // API field names (keys) to entity field names (values)
        public static final String API_CODE = "code";
        public static final String API_STATUS = "status";
        public static final String API_CREATED_AT = "createdAt";
        public static final String API_UPDATED_AT = "updatedAt";
        public static final String API_CREATED_BY = "createdBy";
        public static final String API_UPDATED_BY = "updatedBy";
        public static final String API_ORIGIN_LOCATION = "originLocation";
        public static final String API_DESTINATION_LOCATION = "destinationLocation";
        public static final String API_EXPECTED_START_AT = "expectedStartAt";
        public static final String API_EXPECTED_END_AT = "expectedEndAt";
        public static final String API_ACTUAL_START_AT = "actualStartAt";
        public static final String API_ACTUAL_END_AT = "actualEndAt";
        public static final String API_TRANSPORT_ORDER_CODE = "transportOrderCode";
        public static final String API_TRANSPORT_ORDER_STATUS = "transportOrderStatus";
        public static final String API_ASSIGNMENT_TYPE = "assignmentType";
        public static final String API_VENDOR_ID = "vendorId";
        public static final String API_VEHICLE_REGISTRATION = "vehicleRegistration";
        public static final String API_VEHICLE_TYPE = "vehicleType";
        public static final String API_VEHICLE_ID = "vehicleId";
    }

    // ===== DEFAULT VALUES =====
    public static final String DEFAULT_SORT_BY = CREATED_AT;
    public static final String DEFAULT_SORT_ORDER = "DESC";
}
