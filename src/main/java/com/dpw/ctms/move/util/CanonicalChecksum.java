package com.dpw.ctms.move.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

@Service
@Slf4j
public class CanonicalChecksum {

    private static final String CHECKSUM_ALGORITHM = "SHA-256";

    private final ObjectMapper canonicalObjectMapper;

    public static final String CHECKSUM_FOR_NULL_OBJECT;


    static {
        // Calculate checksum for null once
        CHECKSUM_FOR_NULL_OBJECT = calculateChecksumForStringInternal("null_object_placeholder");
        log.info("Initialized CanonicalChecksumService. Checksum for NULL object: {}", CHECKSUM_FOR_NULL_OBJECT);
    }


    public CanonicalChecksum() {
        this.canonicalObjectMapper = new ObjectMapper();

        // Configure the ObjectMapper for canonical JSON serialization

        // 1. Sort properties alphabetically to ensure consistent field order
        this.canonicalObjectMapper.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true);
        this.canonicalObjectMapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);

        // 2. Consistent Date/Time Formatting (ISO 8601 in UTC)
        this.canonicalObjectMapper.registerModule(new JavaTimeModule());
        this.canonicalObjectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        this.canonicalObjectMapper.setDateFormat(dateFormat);

        // 3. Consistent Null Handling: Exclude null fields.
        this.canonicalObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 4. Disable pretty printing
        this.canonicalObjectMapper.disable(SerializationFeature.INDENT_OUTPUT);
    }

    /**
     * Generates a consistent SHA-256 checksum for any given Java object.
     * The object is first serialized into a canonical JSON string representation
     * using a privately configured ObjectMapper.
     *
     * @param obj The object for which to generate the checksum. Can be null.
     * @return A lowercase hexadecimal string representing the SHA-256 checksum.
     * @throws ChecksumGenerationException if there's an error during JSON serialization or hashing.
     */
    public String generateChecksum(String obj) {
        if (obj == null) {
            return CHECKSUM_FOR_NULL_OBJECT;
        }

        try {
            String canonicalJsonString = this.canonicalObjectMapper.writeValueAsString(obj);
            log.debug("Object for checksum: {}, Canonical JSON: {}", obj, canonicalJsonString);
            return calculateChecksumForStringInternal(canonicalJsonString);
        } catch (JsonProcessingException e) {
            log.error("Error serializing object to JSON for checksum. Object type: {}", obj.getClass().getName(), e);
            throw new ChecksumGenerationException("Failed to serialize object to JSON: " + e.getMessage(), e);
        }
    }

    private static String calculateChecksumForStringInternal(String dataString) {
        try {
            MessageDigest digest = MessageDigest.getInstance(CHECKSUM_ALGORITHM);
            byte[] hashBytes = digest.digest(dataString.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("Fatal error: Hashing algorithm {} not found.", CHECKSUM_ALGORITHM, e);
            // This is a critical internal failure, indicates a setup problem.
            throw new ChecksumGenerationException("Hashing algorithm not found: " + CHECKSUM_ALGORITHM, e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toLowerCase();
    }

    /**
     * Custom runtime exception for errors during checksum generation.
     */
    public static class ChecksumGenerationException extends RuntimeException {
        public ChecksumGenerationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}