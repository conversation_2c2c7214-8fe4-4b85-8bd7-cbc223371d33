package com.dpw.ctms.move.util;

import com.dpw.ctms.move.dto.DateTimeDTO;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeUtil {
    
    private DateTimeUtil() {}
    
    public static DateTimeDTO fromEpochMillis(Long epochMillis) {
        if (epochMillis == null) {
            return new DateTimeDTO();
        }
        
        Instant instant = Instant.ofEpochMilli(epochMillis);
        LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);
        String isoString = utcDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        return DateTimeDTO.builder()
                .epoch(epochMillis)
                .iso(isoString)
                .build();
    }
    
    public static DateTimeDTO fromIsoString(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return new DateTimeDTO();
        }
        
        try {
            LocalDateTime utcDateTime = LocalDateTime.parse(isoString, DateTimeFormatter.ISO_LOCAL_DATE);
            Instant instant = utcDateTime.toInstant(ZoneOffset.UTC);
            Long epochMillis = instant.toEpochMilli();
            
            return DateTimeDTO.builder()
                    .epoch(epochMillis)
                    .iso(isoString)
                    .build();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid ISO format: " + isoString, e);
        }
    }
    
    public static Long toEpochMillis(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime utcDateTime = LocalDateTime.parse(isoString, DateTimeFormatter.ISO_LOCAL_DATE);
            Instant instant = utcDateTime.toInstant(ZoneOffset.UTC);
            return instant.toEpochMilli();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid ISO format: " + isoString, e);
        }
    }
    
    public static String toIsoString(Long epochMillis) {
        if (epochMillis == null) {
            return null;
        }
        
        Instant instant = Instant.ofEpochMilli(epochMillis);
        LocalDateTime utcDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);
        return utcDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}