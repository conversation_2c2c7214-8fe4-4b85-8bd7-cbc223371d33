package com.dpw.ctms.move.util;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.request.OutboundPaginationRequest;
import com.dpw.ctms.move.integration.request.oms.OmsListRequest;
import com.dpw.ctms.move.integration.request.oms.consignment.ConsignmentListFilter;
import org.springframework.stereotype.Component;

/**
 * Utility class for creating OMS service requests
 */
@Component
public class OmsRequestUtil {

    /**
     * Creates a consignment list request with pagination and optional filter by consignment IDs
     *
     * @param consignmentListRequestDTO Optional to filter list by
     * @param paginationDTO Pagination parameters
     * @return ListRequest with appropriate filter
     */
    public static OmsListRequest<ConsignmentListFilter> createConsignmentListRequest(ConsignmentListRequestDTO consignmentListRequestDTO, PaginationDTO paginationDTO) {
        ConsignmentListFilter filter = null;
        if (consignmentListRequestDTO != null) {
            filter = ConsignmentListFilter.builder()
                    .ids(consignmentListRequestDTO.getIds())
                    .build();
        }

        return OmsListRequest.<ConsignmentListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }
    
    /**
     * Creates a consignment list request with pagination and optional filter by consignment IDs
     * @deprecated Use {@link #createConsignmentListRequest(ConsignmentListRequestDTO, PaginationDTO)} instead
     */
    @Deprecated
    public static OmsListRequest<ConsignmentListFilter> createConsignmentListRequest(ConsignmentListRequestDTO consignmentListRequestDTO, int pageNo, int pageSize) {
        return createConsignmentListRequest(consignmentListRequestDTO, PaginationDTO.builder()
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build());
    }
    
    /**
     * Converts PaginationDTO to PaginationRequest
     */
    private static OutboundPaginationRequest convertToPaginationRequest(PaginationDTO paginationDTO) {
        if (paginationDTO == null) {
            return OutboundPaginationRequest.builder()
                    .pageNo(0)
                    .pageSize(10)
                    .build();
        }
        
        return OutboundPaginationRequest.builder()
                .pageNo(paginationDTO.getPageNoOrDefault())
                .pageSize(paginationDTO.getPageSizeOrDefault())
                .build();
    }
}
