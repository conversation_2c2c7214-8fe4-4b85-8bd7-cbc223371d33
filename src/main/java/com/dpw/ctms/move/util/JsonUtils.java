package com.dpw.ctms.move.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
public class JsonUtils {

    private JsonUtils() {
        // Private constructor to prevent instantiation
    }
    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    public static Map<String, Object> toMap(Object source) throws Exception {
        return OBJECT_MAPPER.convertValue(source, new TypeReference<Map<String, Object>>() {});
    }
    
    
    // Convert Object to JSON String
    public static String toJson(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting object to JSON", e);
        }
    }

    // Convert JSON String to Object
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException("Error converting JSON to object", e);
        }
    }

    // Convert JSON String to JsonNode
    public static JsonNode toJsonNodeFromString(String json) {
        try {
            return OBJECT_MAPPER.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException("Error parsing JSON string to JsonNode", e);
        }
    }

    // Convert JSON String to JsonNode
    public static JsonNode toJsonNode(Object json) {
        return OBJECT_MAPPER.convertValue(json,JsonNode.class);
    }

    // Convert JsonNode to Object
    public static <T> T fromJsonNode(JsonNode jsonNode, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.treeToValue(jsonNode, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JsonNode to object", e);
        }
    }

    // Convert Object to Pretty JSON String
    public static String toPrettyJson(Object object) {
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting object to pretty JSON", e);
        }
    }

    // Convert JSON String to List of Objects
    public static <T> List<T> fromJsonToList(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            throw new RuntimeException("Error converting JSON to List", e);
        }
    }

    // Convert JSON String to Map
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        try {
            return OBJECT_MAPPER.readValue(json, OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (IOException e) {
            throw new RuntimeException("Error converting JSON to Map", e);
        }
    }

    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return OBJECT_MAPPER.convertValue(fromValue, toValueType);
    }

    public static <T> T fromJsonNode(JsonNode node, TypeReference<T> typeReference) {
        try {
            return new ObjectMapper().readValue(new ObjectMapper().treeAsTokens(node), typeReference);
        } catch (IOException e) {
            throw new RuntimeException("Deserialization failed", e);
        }
    }
}

