package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.dto.resource.FacilityListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.UomListRequestDTO;
import com.dpw.ctms.move.integration.request.resource.ResourceListRequest;
import com.dpw.ctms.move.integration.request.resource.UomListFilter;
import com.dpw.ctms.move.integration.request.resource.facility.FacilityListFilter;
import com.dpw.ctms.move.integration.response.resource.ResourceApiResponse;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.feignClient.ResourceClient;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.ctms.move.util.ResourceRequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.dpw.ctms.move.constants.ResourceServiceConstants.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceServiceAdapter extends BaseServiceAdapter {
    private final ResourceClient resourceFeignClient;

    @Override
    public String getServiceName() {
        return RESOURCE_SERVICE;
    }


    public ListResponse<FacilityRecord> getFacilityList(FacilityListRequestDTO facilityListRequestDTO, PaginationDTO paginationDTO) {
        return execute(GET_FACILITY_LIST_CONTEXT, () -> {
            ResourceListRequest<FacilityListFilter> request =
                    ResourceRequestUtil.createFacilityListRequest(facilityListRequestDTO, paginationDTO);
            log.info("Facility List Request: {}", JsonUtils.toJson(request));
            ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> resourceFacilityResponse = resourceFeignClient.getFacilityList(request);
            return Optional.ofNullable(resourceFacilityResponse)
                    .map(ResponseEntity::getBody)
                    .map(ResourceApiResponse::getData)
                    .orElse(new ListResponse<>());
        });
    }

    public ListResponse<UomRecord> getUomList(UomListRequestDTO uomListRequestDTO, PaginationDTO paginationDTO) {
        return execute(GET_UOM_LIST_CONTEXT, () -> {
            ResourceListRequest<UomListFilter> request =
                    ResourceRequestUtil.createUomListRequest(uomListRequestDTO, paginationDTO);
            log.info("Uom List Request: {}", JsonUtils.toJson(request));
            ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> resourceUomResponse = resourceFeignClient.getUomList(request);
            return Optional.ofNullable(resourceUomResponse)
                    .map(ResponseEntity::getBody)
                    .map(ResourceApiResponse::getData)
                    .orElse(new ListResponse<>());
        });
    }
}
