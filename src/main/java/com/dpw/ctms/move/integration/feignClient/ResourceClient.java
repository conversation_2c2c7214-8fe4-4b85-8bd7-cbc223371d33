package com.dpw.ctms.move.integration.feignClient;

import com.dpw.ctms.move.constants.ResourceServiceConstants;
import com.dpw.ctms.move.integration.request.resource.ResourceListRequest;
import com.dpw.ctms.move.integration.request.resource.UomListFilter;
import com.dpw.ctms.move.integration.request.resource.facility.FacilityListFilter;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.resource.ResourceApiResponse;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "resource-service", url = "${tms.service-url.resource}")
public interface ResourceClient {

    @PostMapping(ResourceServiceConstants.Endpoints.GET_FACILITY_LIST)
    ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> getFacilityList(@RequestBody ResourceListRequest<FacilityListFilter> request);

    @PostMapping(ResourceServiceConstants.Endpoints.GET_UOM_LIST)
    ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> getUomList(@RequestBody ResourceListRequest<UomListFilter> request);

}