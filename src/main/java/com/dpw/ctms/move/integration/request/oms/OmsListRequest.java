package com.dpw.ctms.move.integration.request.oms;


import com.dpw.ctms.move.integration.request.OutboundPaginationRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OmsListRequest<T> {
    private OutboundPaginationRequest pagination;
    private T filter;
}
