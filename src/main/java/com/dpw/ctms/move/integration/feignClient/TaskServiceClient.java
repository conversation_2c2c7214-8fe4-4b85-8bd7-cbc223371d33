package com.dpw.ctms.move.integration.feignClient;

import com.dpw.ctms.move.config.FeignConfig;
import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.response.ListResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "taskService", url = "${tms.service-url.task-service:http://localhost:8081}", configuration = FeignConfig.class)
public interface TaskServiceClient {

    @PostMapping(TaskServiceConstants.Endpoints.TASK_INSTANCE_REGISTER)
    ResponseEntity<ListResponse<TaskInstanceRegistrationResponse>> registerTaskInstance(
            TaskInstanceRegistrationRequest taskInstanceRegistrationRequest);

}