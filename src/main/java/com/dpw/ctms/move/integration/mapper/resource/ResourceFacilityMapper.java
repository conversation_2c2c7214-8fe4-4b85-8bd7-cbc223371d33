package com.dpw.ctms.move.integration.mapper.resource;

import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import org.mapstruct.*;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ResourceFacilityMapper {

    @Mapping(target = "ownershipType", source = "ownershipType", qualifiedByName = "mapLabelValue")
    @Mapping(target = "pointOfContactDetails", source = "pointOfContactDetails", qualifiedByName = "mapPointOfContact")
    @Mapping(target = "addressDetails", source = "addressDetails", qualifiedByName = "mapAddress")
    FacilityDetailsDTO mapToFacilityDetails(FacilityRecord facilityRecord);

    @Named("mapPointOfContact")
    default FacilityDetailsDTO.PointOfContactDetails mapPointOfContactDetails(
            FacilityRecord.PointOfContactResponse pocResponse) {
        if (pocResponse == null) {
            return null;
        }

        return FacilityDetailsDTO.PointOfContactDetails.builder()
                .firstName(pocResponse.getFirstName())
                .lastName(pocResponse.getLastName())
                .emailId(pocResponse.getEmailId())
                .phoneNumber(pocResponse.getPhoneNumber())
                .countryCode(pocResponse.getCountryCode())
                .build();
    }

    @Named("mapAddress")
    default FacilityDetailsDTO.AddressDetails mapAddressDetails(FacilityRecord.AddressResponse addressResponse) {
        if (addressResponse == null) {
            return null;
        }

        return FacilityDetailsDTO.AddressDetails.builder()
                .addressLine(addressResponse.getAddressLine())
                .postalCode(addressResponse.getPostalCode())
                .suburbName(addressResponse.getSuburbDetails() != null ? 
                        addressResponse.getSuburbDetails().getName() : null)
                .cityName(addressResponse.getCityDetails() != null ? 
                        addressResponse.getCityDetails().getName() : null)
                .provinceName(addressResponse.getProvinceDetails() != null ? 
                        addressResponse.getProvinceDetails().getName() : null)
                .countryName(addressResponse.getCountryDetails() != null ? 
                        addressResponse.getCountryDetails().getName() : null)
                .build();
    }

    @Named("mapLabelValue")
    default String mapLabelValue(LabelValue labelValue) {
        return labelValue != null ? labelValue.getValue() : null;
    }
}