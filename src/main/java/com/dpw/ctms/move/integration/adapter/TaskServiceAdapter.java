package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.integration.feignClient.TaskServiceClient;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.response.ListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class TaskServiceAdapter extends BaseServiceAdapter {

    private final TaskServiceClient taskServiceClient;

    public List<TaskInstanceRegistrationResponse> registerTaskInstance (
            TaskInstanceRegistrationRequest taskInstanceRegistrationRequest) {
        ListResponse<TaskInstanceRegistrationResponse> response =
                execute("registerTaskInstance", () -> taskServiceClient
                        .registerTaskInstance(taskInstanceRegistrationRequest).getBody());
        return response.getData();
    }

    @Override
    public String getServiceName() {
        return "Task Service";
    }
}
