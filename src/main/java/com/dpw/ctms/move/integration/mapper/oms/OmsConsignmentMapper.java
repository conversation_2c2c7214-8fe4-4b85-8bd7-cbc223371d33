package com.dpw.ctms.move.integration.mapper.oms;

import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.util.DateTimeUtil;
import org.mapstruct.*;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OmsConsignmentMapper {

    @Mapping(target = "consignmentId", expression = "java(String.valueOf(consignmentRecord.getId()))")
    @Mapping(target = "consignmentCode", source = "code")
    @Mapping(target = "lineItemId", source = "lineItemId")
    @Mapping(target = "lineItemCode", source = "lineItemCode")
    @Mapping(target = "customerOrderId", source = "customerOrderId")
    @Mapping(target = "customerOrderCode", source = "customerOrderCode")
    @Mapping(target = "specialInstructions", source = "lineItemMetadata.specialInstructions", defaultValue = "")
    @Mapping(target = "customerId", source = "customer.id")
    @Mapping(target = "originFacilityId", source = "origin.id")
    @Mapping(target = "destinationFacilityId", source = "destination.id")
    @Mapping(target = "expectedPickupTime", source = "expectedPickupTime", qualifiedByName = "mapTimeInfo")
    @Mapping(target = "expectedDeliveryTime", source = "expectedDeliveryTime", qualifiedByName = "mapTimeInfo")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapLabelValue")
    @Mapping(target = "movementType", source = "movementType", qualifiedByName = "mapLabelValue")
    @Mapping(target = "productDetailsDTO", source = "products", qualifiedByName = "mapProductDetails")
    @Mapping(target = "shipmentIds", expression = "java(new java.util.ArrayList<>())")
    @Mapping(target = "customerOrderMetaData", source = "customerOrderMetadata")
    ConsignmentDetailsDTO mapToConsignmentDetails(ConsignmentRecord consignmentRecord);

    @Named("mapTimeInfo")
    default DateTimeDTO mapTimeInfo(ConsignmentRecord.TimeInfo timeInfo) {
        if (timeInfo == null || timeInfo.getEpoch() == null) {
            return null;
        }
        return DateTimeUtil.fromEpochMillis(timeInfo.getEpoch());
    }

    @Named("mapLabelValue")
    default String mapLabelValue(LabelValue labelValue) {
        return labelValue != null ? labelValue.getValue() : null;
    }

    @Named("mapProductDetails")
    default ProductDetailsDTO mapProductDetails(List<ConsignmentRecord.Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        return products.stream()
                .findFirst()
                .map(this::mapSingleProduct)
                .orElse(null);
    }

    default ProductDetailsDTO mapSingleProduct(ConsignmentRecord.Product product) {
        if (product == null) {
            return null;
        }

        var builder = ProductDetailsDTO.builder()
                .id(product.getId())
                .resourceId(Optional.ofNullable(product.getResourceDetails())
                        .map(ConsignmentRecord.Product.ResourceDetails::getId)
                        .orElse(null));

        if (!CollectionUtils.isEmpty(product.getProperties())) {
            builder.properties(
                    product.getProperties().stream()
                            .map(property -> ProductDetailsDTO.PropertyDetails.builder()
                                    .name(Optional.ofNullable(property.getPropertyName())
                                            .map(LabelValue::getValue)
                                            .orElse(null))
                                    .value(property.getPropertyValue())
                                    .resourceUomId(Optional.ofNullable(property.getUnitOfMeasurement())
                                            .map(ConsignmentRecord.Product.Property.UnitOfMeasurement::getId)
                                            .orElse(null))
                                    .build())
                            .collect(Collectors.toMap(ProductDetailsDTO.PropertyDetails::getName, p -> p))
            );
        }

        return builder.build();
    }
}