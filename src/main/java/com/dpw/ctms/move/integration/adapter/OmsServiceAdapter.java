package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.request.oms.OmsListRequest;
import com.dpw.ctms.move.integration.request.oms.consignment.ConsignmentListFilter;
import com.dpw.ctms.move.integration.response.oms.OmsApiResponse;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.feignClient.OmsClient;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.ctms.move.util.OmsRequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.dpw.ctms.move.constants.OmsServiceConstants.GET_CONSIGNMENT_LIST_CONTEXT;
import static com.dpw.ctms.move.constants.OmsServiceConstants.OMS_SERVICE;

@Slf4j
@Component
@RequiredArgsConstructor
public class OmsServiceAdapter extends BaseServiceAdapter {
    private final OmsClient omsFeignClient;

    @Override
    public String getServiceName() {
        return OMS_SERVICE;
    }

    public OmsListResponse<ConsignmentRecord> getConsignmentList(ConsignmentListRequestDTO consignmentListRequestDTO, PaginationDTO paginationDTO) {
        return execute(GET_CONSIGNMENT_LIST_CONTEXT, () -> {
            OmsListRequest<ConsignmentListFilter> request =
                    OmsRequestUtil.createConsignmentListRequest(consignmentListRequestDTO, paginationDTO);
            log.info("Consignment List Request: {}", JsonUtils.toJson(request));
            ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> consignmentListResponse = this.omsFeignClient.getConsignmentList(request);
            return Optional.ofNullable(consignmentListResponse)
                    .map(ResponseEntity::getBody)
                    .map(OmsApiResponse::getData)
                    .orElse(new OmsListResponse<>());
        });
    }

}
