package com.dpw.ctms.move.integration.response.resource.facility;

import com.dpw.ctms.move.integration.response.LabelValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class FacilityRecord {
    private Long id;
    private String code;
    private String name;
    private LabelValue status;
    private LabelValue ownershipType;
    private List<IdCodeNameResponse> mappedEntities;
    private String instructions;
    private PointOfContactResponse pointOfContactDetails;
    private AddressResponse addressDetails;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class IdCodeNameResponse {
        private Long id;
        private String name;
        private String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PointOfContactResponse {
        private Long id;
        private String firstName;
        private String lastName;
        private String designation;
        private String emailId;
        private String phoneNumber;
        private String countryCode;
        private List<String> additionalEmailIds;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class AddressResponse {
        private String externalAddressCode;
        private String addressLine;
        private String postalCode;
        private SuburbResponse suburbDetails;
        private CityResponse cityDetails;
        private ProvinceResponse provinceDetails;
        private CountryResponse countryDetails;
        private PinPointResponse point;


        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class SuburbResponse {
            private String name;
            private String externalSuburbCode;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        public static class CityResponse {
            private String name;
            private String externalCityCode;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class ProvinceResponse {
            private Long id;
            private String name;
            private String externalProvinceCode;
        }



        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class CountryResponse {
            private String name;
            private String externalCountryCode;
        }


        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class PinPointResponse {
            private String lat;
            private String lng;
        }

    }
}