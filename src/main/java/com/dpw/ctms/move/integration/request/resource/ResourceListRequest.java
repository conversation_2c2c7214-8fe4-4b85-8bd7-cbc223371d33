package com.dpw.ctms.move.integration.request.resource;


import com.dpw.ctms.move.integration.request.OutboundPaginationRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceListRequest<T> {
    private OutboundPaginationRequest pagination;
    private T filter;
}
