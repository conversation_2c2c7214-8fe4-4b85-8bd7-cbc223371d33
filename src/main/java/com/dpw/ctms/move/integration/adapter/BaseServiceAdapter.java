package com.dpw.ctms.move.integration.adapter;

import com.dpw.tmsutils.exception.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.util.function.Supplier;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.*;

@Slf4j
@Getter
public abstract class BaseServiceAdapter implements ServiceAdapter {

    @Override
    public <T> T execute(String context, Supplier<T> serviceCall) {
        String serviceName = getServiceName();
        try {
            log.info("Executing {} serviceCall on {} service", context, serviceName);
            T result = serviceCall.get();
            log.info("Successfully executed {} serviceCall on {} service. Response: {}", context, serviceName, result);
            return result;
        } catch (TMSFeignException ex) {
            handleFeignException(context, serviceName, ex);
            throw new ExternalApiException("Unhandled external service error", HttpStatus.INTERNAL_SERVER_ERROR); // fallback
        } catch (Exception e) {
            String msg = String.format("[%s] Unexpected error processing: %s", context, e.getMessage());
            log.error(msg, e);
            throw new GenericException("Unexpected error", msg);
        }
    }

    private void handleFeignException(String context, String serviceName, TMSFeignException ex) {
        String msg = String.format("[%s] Error in %s API call: %s", context, serviceName, ex.getMessage());
        log.error(msg, ex);
        HttpStatus status = HttpStatus.resolve(ex.getStatus());

        if (status == null) {
            throw new TMSException(UNKNOWN_ERROR.name(), "Unknown error");
        }

        switch (status) {
            case NOT_FOUND -> throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), "Not found");
            case BAD_REQUEST -> throw new TMSException(INVALID_REQUEST.name(), "Bad request");
            case FORBIDDEN -> throw new TMSException(INTEGRATION_ERROR.name(), "Access denied");
            case GATEWAY_TIMEOUT, BAD_GATEWAY, INTERNAL_SERVER_ERROR -> throw new TMSException(INTERNAL_ERROR.name(), "Server error");
            default -> throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(),"Unknown external error");
        }
    }
}

