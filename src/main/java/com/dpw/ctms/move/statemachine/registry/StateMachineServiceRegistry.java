package com.dpw.ctms.move.statemachine.registry;

import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.impl.ShipmentStateMachineServiceImpl;
import com.dpw.ctms.move.statemachine.impl.TaskStateMachineServiceImpl;
import com.dpw.ctms.move.statemachine.impl.TripStateMachineServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StateMachineServiceRegistry {

    private final TaskStateMachineServiceImpl taskStateMachineService;
    private final ShipmentStateMachineServiceImpl shipmentStateMachineService;
    private final TripStateMachineServiceImpl tripStateMachineService;

    public IStateMachineService<?> getService(StateMachineEntityType entityType) {
        return switch (entityType) {
            case TASK -> taskStateMachineService;
            case SHIPMENT -> shipmentStateMachineService;
            case TRIP -> tripStateMachineService;
        };
    }
}
