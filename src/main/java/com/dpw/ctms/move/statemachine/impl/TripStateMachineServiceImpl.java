package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.service.ITripService;
import org.springframework.stereotype.Service;

@Service
public class TripStateMachineServiceImpl extends AbstractStateMachineService<Trip> {

    private final ITripService tripService;

    protected TripStateMachineServiceImpl(StateMachineFactoryService stateMachineFactoryService,
                                          ITripService tripService) {
        super(stateMachineFactoryService);
        this.tripService = tripService;
    }

    @Override
    protected StateMachineEntityType getStateMachineEntityType() {
        return StateMachineEntityType.TRIP;
    }

    @Override
    protected Trip findEntityById(Long id) {
        return tripService.findTripById(id);
    }

    @Override
    protected String extractState(Trip entity) {
        return entity.getStatus().name();
    }

    @Override
    protected void updateState(Trip entity, String newState) {
        entity.setStatus(TripStatus.valueOf(newState));
    }

    @Override
    protected void saveEntity(Trip entity) {
        tripService.saveTrip(entity);
    }
}
