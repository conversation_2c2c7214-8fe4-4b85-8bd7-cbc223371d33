package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.enums.ConfigType;
import com.dpw.ctms.move.integration.ConfigServiceIntegrator;
import com.dpw.ctms.move.statemachine.IStateMachineConfigFetcher;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ShipmentStateMachineConfigFetcher implements IStateMachineConfigFetcher {

    private final ConfigServiceIntegrator configServiceIntegrator;

    @Override
    public StateTransitionHolderDTO fetchStateMachineConfig(String tenantId) {
        StateMachineConfigService stateMachineConfigService = configServiceIntegrator.getConfigService(
                ConfigType.STATE_MACHINE, StateMachineConfigService.class);
        return stateMachineConfigService.getShipmentStateMachineConfig(tenantId);
    }
}
