package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.service.IShipmentService;
import org.springframework.stereotype.Service;

@Service
public class ShipmentStateMachineServiceImpl extends AbstractStateMachineService<Shipment> {

    private final IShipmentService shipmentService;

    protected ShipmentStateMachineServiceImpl(StateMachineFactoryService stateMachineFactoryService,
                                              IShipmentService shipmentService) {
        super(stateMachineFactoryService);
        this.shipmentService = shipmentService;
    }

    @Override
    protected StateMachineEntityType getStateMachineEntityType() {
        return StateMachineEntityType.SHIPMENT;
    }

    @Override
    protected Shipment findEntityById(Long id) {
        return shipmentService.findShipmentById(id);
    }

    @Override
    protected String extractState(Shipment entity) {
        return entity.getStatus().name();
    }

    @Override
    protected void updateState(Shipment entity, String newState) {
        entity.setStatus(ShipmentStatus.valueOf(newState));
    }

    @Override
    protected void saveEntity(Shipment entity) {
        shipmentService.saveShipment(entity);
    }
}
