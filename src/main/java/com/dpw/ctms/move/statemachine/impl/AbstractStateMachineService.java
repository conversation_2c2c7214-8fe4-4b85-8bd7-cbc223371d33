package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class AbstractStateMachineService<T> implements IStateMachineService<T> {

    protected final StateMachineFactoryService stateMachineFactoryService;

    protected AbstractStateMachineService(StateMachineFactoryService stateMachineFactoryService) {
        this.stateMachineFactoryService = stateMachineFactoryService;
    }

    @Override
    public void handleEvent(String tenantId, String event, Long id) {
        log.info("Received status change event for tenantId {} event {} and entityType {}", tenantId, event, getStateMachineEntityType());
        StateMachine<String, String> stateMachine = stateMachineFactoryService.createStateMachine(tenantId, getStateMachineEntityType());

        T entity = findEntityById(id);
        String currentState = extractState(entity);

        resetStateMachine(stateMachine, currentState);
        startStateMachine(stateMachine);
        sendEvent(stateMachine, event, entity);
    }

    private void startStateMachine(StateMachine<String, String> stateMachine) {
        stateMachine.startReactively().subscribe();
    }

    private void resetStateMachine(StateMachine<String, String> stateMachine, String state) {
        stateMachine.stopReactively().subscribe();
        DefaultStateMachineContext<String, String> stateMachineContext = new DefaultStateMachineContext<>(
                state, null, null, null);
        stateMachine.getStateMachineAccessor().doWithAllRegions(accessor ->
                accessor.resetStateMachineReactively(stateMachineContext).subscribe());
    }

    private void sendEvent(StateMachine<String, String> stateMachine, String event, T entity) {
        Message<String> message = MessageBuilder.withPayload(event).build();
        stateMachine.sendEvent(Mono.just(message))
                .then(Mono.fromRunnable(() -> {
                    String newState = stateMachine.getState().getId();
                    updateState(entity, newState);
                    saveEntity(entity);
                }))
                .block();
    }

    protected abstract StateMachineEntityType getStateMachineEntityType();
    protected abstract T findEntityById(Long id);
    protected abstract String extractState(T entity);
    protected abstract void updateState(T entity, String newState);
    protected abstract void saveEntity(T entity);
}
