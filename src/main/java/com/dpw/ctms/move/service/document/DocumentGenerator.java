package com.dpw.ctms.move.service.document;

import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.enums.Vendor;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.concurrent.CompletableFuture;

public interface DocumentGenerator<T> {
    /**
     * Fills the document generation context with all required data
     */
    CompletableFuture<DocumentGenerationContextDTO<T>> generateContext(String tripCode, Vendor vendor);

    /**
     * Converts the filled context to JSON
     */
    String generateJson(String tripCode, Vendor vendor);

    /**
     * Returns the document type this generator handles
     */
    DocumentType getDocumentType();
}

