package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;

import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Service
@LogExecutionTime
@RequiredArgsConstructor
@Slf4j
public class EventProcessorServiceImpl<REQ> implements IEventProcessorService<REQ> {
    private final Map<String, IEventRequestPublisher<REQ>> eventRequestProcessorMap = new HashMap<>();

    @Override
    public void addRequestProcessor(String actionCode, IEventRequestPublisher<REQ> eventRequestProcessor) {
        log.info("Registering Event request Processor {}: with action: {}",
                StringEscapeUtils.escapeJava(eventRequestProcessor.getClass().getSimpleName()),
                StringEscapeUtils.escapeJava(actionCode));
        if (eventRequestProcessorMap.get(actionCode) != null) {
            return;
        }
        eventRequestProcessorMap.put(actionCode, eventRequestProcessor);
    }

    @Override
    public IntegratorMessageResponse processRequest(String actionCode, IntegratorMessageRequest<REQ> request) {
        if (StringUtils.isEmpty(actionCode)) {
            StringBuilder sb = new StringBuilder("Action type is invalid: ")
                    .append(actionCode);
            log.error(sb.toString());
            throw new TMSException(INVALID_REQUEST.name(), sb.toString());
        }
        IEventRequestPublisher<REQ> eventRequestProcessor = eventRequestProcessorMap.get(actionCode);
        if (Objects.isNull(eventRequestProcessor)) {
            StringBuilder sb = new StringBuilder("No processor found for given action: ")
                    .append(actionCode);
            log.error(sb.toString());
            throw new TMSException(INVALID_REQUEST.name(), sb.toString());
        }
        return IntegratorMessageResponse.builder()
                .success(eventRequestProcessor.process(request))
                .build();
    }
}
