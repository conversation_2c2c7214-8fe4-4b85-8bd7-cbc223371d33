package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.mapper.TripMapper;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.tmsutils.exception.TMSException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dpw.ctms.move.enums.StopStatus.DISCARDED;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Slf4j
@Service
@RequiredArgsConstructor
public class TripDataServiceImpl implements ITripDataService {
    private final StopRepository stopRepository;
    private final TripRepository tripRepository;
    private final TripMapper tripMapper;
    private final EntityManager entityManager;

    @Override
    public Trip getTripByCode(String tripCode) {
        return tripRepository.findByCode(tripCode)
                .orElseThrow(() -> new TMSException(INVALID_REQUEST.name(), "Trip not found with code: " + tripCode));


    }

    @Override
    public Trip getTripByCodeWithAllDetails(String tripCode) {
        try {
            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaQuery<Trip> query = cb.createQuery(Trip.class);
            Root<Trip> root = query.from(Trip.class);

            root.fetch(TripFieldConstants.STOPS, JoinType.LEFT);
            root.fetch(TripFieldConstants.SHIPMENTS, JoinType.LEFT);
            root.fetch(TripFieldConstants.VEHICLE_OPERATOR_RESOURCES, JoinType.LEFT);
            root.fetch(TripFieldConstants.VEHICLE_RESOURCE, JoinType.LEFT);
            root.fetch(TripFieldConstants.TRAILER_RESOURCES, JoinType.LEFT);
            root.fetch(TripFieldConstants.TRANSPORT_ORDER, JoinType.LEFT);

            query.select(root);

            query.where(cb.equal(root.get(TripFieldConstants.CODE), tripCode));

            Trip trip = entityManager.createQuery(query).getSingleResult();

            return trip;
        } catch (Exception e) {
            log.error("Error occurred while fetching trip with code: {}", tripCode, e);
            throw new TMSException(INVALID_REQUEST.name(), "Trip not found with code: " + tripCode);
        }
    }

    @Override
    public Page<Stop> getTripStopByTripCode(String tripCode, Pagination paginationRequestSo) {
        return stopRepository.findAllByTrip_CodeAndStatusNotInOrTrip_CodeAndStatusIsNull(tripCode,
                List.of(DISCARDED.name()), tripCode, PageRequest.of(
                        paginationRequestSo.getPageNo(),
                        paginationRequestSo.getPageSize()
                ));
    }

    @Override
    public Long getTotalStopCount(String tripCode) {
        return stopRepository.countDistinctByTrip_CodeAndStatusNotIn(tripCode, List.of(DISCARDED.name()));
    }
}
