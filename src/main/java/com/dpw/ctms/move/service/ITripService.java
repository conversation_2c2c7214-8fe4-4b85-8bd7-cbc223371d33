package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.response.TripViewResponse;

public interface ITripService {
    ListResponse<TripListingResponse> listTrips(TripListingRequest tripListingRequest);
    TripViewResponse getTripView(String tripCode);
    Trip findTripById(Long id);
    Trip saveTrip(Trip trip);
}
