package com.dpw.ctms.move.service;

import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;

public interface IEventProcessorService<REQ> {

    void addRequestProcessor(String actionCode, IEventRequestPublisher<REQ> eventRequestProcessor);

    IntegratorMessageResponse processRequest(String actionCode, IntegratorMessageRequest<REQ> request);
}
