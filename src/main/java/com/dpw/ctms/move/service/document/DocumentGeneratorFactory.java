package com.dpw.ctms.move.service.document;

import com.dpw.ctms.move.enums.DocumentType;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * Factory to retrieve the appropriate DocumentGenerator implementation by DocumentType.
 */

@Component
public class DocumentGeneratorFactory {

    private final Map<DocumentType, DocumentGenerator<?>> generators;

    /**
     * Autowire all DocumentGenerator beans and register them by type.
     */
    public DocumentGeneratorFactory(List<DocumentGenerator<?>> generatorList) {
        this.generators = new EnumMap<>(DocumentType.class);
        for (DocumentGenerator<?> gen : generatorList) {
            generators.put(gen.getDocumentType(), gen);
        }
    }

    /**
     * Retrieve the generator for the given type.
     * @throws IllegalArgumentException if no generator is found.
     */
    public DocumentGenerator<?> getGenerator(DocumentType type) {
        DocumentGenerator<?> generator = generators.get(type);
        if (generator == null) {
            throw new IllegalArgumentException("No DocumentGenerator found for type: " + type);
        }
        return generator;
    }
}
