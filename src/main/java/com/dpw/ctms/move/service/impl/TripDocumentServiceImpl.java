package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.response.TripDocumentDownloadResponse;
import com.dpw.ctms.move.service.ITripDocumentService;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.PropertyConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class TripDocumentServiceImpl implements ITripDocumentService {

    private final DocumentGeneratorFactory documentGeneratorFactory;
    private final DocumentRepository documentRepository;
    private final CanonicalChecksum canonicalChecksum;
    private final DocumentService documentService;
    private final ConfigService configService;
    private final DocumentMapper documentMapper;

    public TripDocumentDownloadResponse downloadTripBolDocument(
            String tripCode,
            Vendor vendor
    ) {
        log.info("Generating BOL document for tripCode: {}", tripCode);
        DocumentGenerator<?> generator = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        String json = generator.generateJson(tripCode, vendor);
        String checksumString = canonicalChecksum.generateChecksum(json);

        Optional<Document> document = documentRepository.findByTripCodeAndDocumentTypeAndChecksum(tripCode, DocumentType.BOL, checksumString);

        if (document.isEmpty()) {
            DocumentDTO documentDTO = DocumentDTO.builder()
                    .tripCode(tripCode)
                    .documentType(DocumentType.BOL)
                    .checksum(checksumString)
                    .build();
            return generateAndSaveDocument(documentDTO, vendor, json);
        } else {
            // Always get fresh presigned URL from document service using fileIdentifier
            GetDownloadPreSignedURLRequest downloadPreSignedURLRequest =
                    GetDownloadPreSignedURLRequest
                            .builder()
                            .fileIdentifiers(Optional.ofNullable(document.get().getFileIdentifier())
                                .map(fileId -> Collections.singletonList(UUID.fromString(fileId)))
                                .orElse(Collections.emptyList()))
                            .readExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                            .build();

            DocumentServiceResponse<List<DownloadPreSignedURLResponse>> documentServiceResponse = documentService.getDownloadPreSignedURLWithToken(downloadPreSignedURLRequest);
            String presignedUrl = documentServiceResponse.getData().getFirst().getPreSignedUrl();
            return TripDocumentDownloadResponse.builder()
                    .presignedDownloadUrl(presignedUrl)
                    .build();
        }

    }

    private TripDocumentDownloadResponse generateAndSaveDocument(DocumentDTO documentDTO, Vendor vendor, String json) {
        log.info("Generating and saving document for tripCode: {}, documentType: {}", 
                documentDTO.getTripCode(), documentDTO.getDocumentType());

        JsonNode config = configService.getConfig( ConfigConstants.BOL_CONFIG, vendor);
        String templateId = config.get("templateId").asText();
        log.info("Template id: {}", templateId);
        
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID not found in config for documentType: " + 
                    documentDTO.getDocumentType() + ", vendor: " + vendor);
        }

        PrintBolRequest<JsonNode> printBolRequest = PrintBolRequest.<JsonNode>builder()
                .data(JsonUtils.toJsonNodeFromString(json))
                .bolConfiguration(PrintBolRequest.BolConfiguration.builder()
                        .type(FILE_TYPE_PDF).responseType(PRESIGNED_DOWNLOAD_LINK).linkExpiryDuration(LINK_EXPIRY_DURATION)
                        .build())
                .build();

        DocumentServiceResponse<PrintBolResponse> response = documentService.getBol(printBolRequest, templateId);
        log.info("Response from DocumentService: {}", response);
        documentDTO.setFileIdentifier(response.getData().getFileIdentifier());

        documentRepository.save(documentMapper.toEntity(documentDTO));
        log.info("Saved document to database for tripCode: {}, documentType: {}", 
                documentDTO.getTripCode(), documentDTO.getDocumentType());
        
        return TripDocumentDownloadResponse.builder()
                .presignedDownloadUrl(response.getData().getPresignedDownloadUrl())
                .build();
    }

}
