package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ShipmentDetailsDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.service.IConsignmentService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ConsignmentService implements IConsignmentService {
    private final IShipmentService shipmentService;
    private final ITaskParamService taskParamService;

    @Override
    public Map<String, ShipmentDetailsDTO> getShipmentDetailsFromTask(List<Task> tasks) {
        // Step 1: Build taskCode → shipmentCode map
        Map<String, String> taskToShipmentCode = new HashMap<>();
        for (Task task : tasks) {
            taskParamService.getShipmentCode(task)
                    .map(ParamValueShipmentDTO::getCode)
                    .ifPresent(shipmentCode -> taskToShipmentCode.put(task.getCode(), shipmentCode));
        }

        // Step 2: Get unique shipment codes and fetch shipments
        Set<String> shipmentCodes = new HashSet<>(taskToShipmentCode.values());
        List<Shipment> shipments = shipmentService.getAllByCodes(shipmentCodes);

        // Step 3: Build shipment code → ShipmentDetailsDTO map for lookup
        Map<String, ShipmentDetailsDTO> shipmentDetailsMap = shipments.stream()
                .collect(Collectors.toMap(
                        Shipment::getCode,
                        shipment -> new ShipmentDetailsDTO(shipment.getCode(),
                                shipment.getExternalConsignmentId(),
                                shipment.getExternalCustomerOrderId())));

        // Step 4: Build final task code → ShipmentDetailsDTO map
        return taskToShipmentCode.entrySet().stream()
                .filter(entry -> shipmentDetailsMap.containsKey(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,  // task code
                        entry -> shipmentDetailsMap.get(entry.getValue()))); // ShipmentDetailsDTO
    }

}
