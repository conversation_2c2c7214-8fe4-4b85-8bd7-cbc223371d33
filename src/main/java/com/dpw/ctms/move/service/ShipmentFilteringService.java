package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.ShipmentMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.data.domain.Sort.Direction;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Service responsible for filtering shipments based on various criteria.
 * Provides comprehensive filtering capabilities with pagination and sorting support.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentFilteringService {

    private final ShipmentRepository shipmentRepository;
    private final ShipmentMapper shipmentMapper;

    // Default pagination values
    private static final int DEFAULT_PAGE_NO = 0;
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 500;

    // Default sorting values
    private static final String DEFAULT_SORT_BY = ShipmentFieldConstants.DEFAULT_SORT_BY;
    private static final String DEFAULT_SORT_ORDER = ShipmentFieldConstants.DEFAULT_SORT_ORDER;

    /**
     * Main method to filter shipments based on the provided request
     *
     * @param request The filtering request containing pagination, sorting, and filter criteria
     * @return List of filtered and mapped shipment responses
     */
    @Transactional(readOnly = true)
    public ListResponse<ShipmentListingResponse> filterShipments(ShipmentListingRequest request) {
        try {
            log.info("Starting shipment filtering with request: {}", request);

            Specification<Shipment> specification = buildSpecification(request.getFilter());

            Pageable pageable = createPageable(request.getPagination(), request.getSort());

            Page<Shipment> shipmentPage = shipmentRepository.findAll(specification, pageable);

            Long totalElements = shipmentPage.getTotalElements();
            log.info("Found {} shipments matching criteria", totalElements);

            // Map entities to response DTOs
            List<ShipmentListingResponse> shipmentResponses = shipmentPage.getContent().stream()
                    .map(shipmentMapper::toResponse)
                    .collect(Collectors.toList());

            return ListResponse.<ShipmentListingResponse>builder()
                    .data(shipmentResponses)
                    .totalRecords(totalElements)
                    .build();

        } catch (Exception e) {
            log.error("Error occurred while filtering shipments", e);
            throw new RuntimeException("Failed to filter shipments", e);
        }
    }

    /**
     * Builds JPA Specification based on filter criteria
     *
     * @param filter The filter criteria
     * @return Combined specification for all filter conditions
     */
    private Specification<Shipment> buildSpecification(ShipmentListingRequest.Filter filter) {
        Specification<Shipment> spec = Specification.where(null);

        if (filter == null) {
            return spec;
        }

        // Filter by trip IDs
        if (!CollectionUtils.isEmpty(filter.getTripIds())) {
            spec = spec.and(tripIdsIn(filter.getTripIds()));
        }

        // Filter by shipment IDs
        if (!CollectionUtils.isEmpty(filter.getShipmentIds())) {
            spec = spec.and(shipmentIdsIn(filter.getShipmentIds()));
        }

        // Filter by consignment IDs
        if (!CollectionUtils.isEmpty(filter.getConsignmentIds())) {
            spec = spec.and(consignmentIdsIn(filter.getConsignmentIds()));
        }

        // Filter by trip statuses
        if (!CollectionUtils.isEmpty(filter.getTripStatuses())) {
            spec = spec.and(tripStatusesIn(filter.getTripStatuses()));
        }

        // Filter by transport order statuses
        if (!CollectionUtils.isEmpty(filter.getTransportOrderStatuses())) {
            spec = spec.and(transportOrderStatusesIn(filter.getTransportOrderStatuses()));
        }

        // Filter by shipment statuses
        if (!CollectionUtils.isEmpty(filter.getShipmentStatuses())) {
            spec = spec.and(shipmentStatusesIn(filter.getShipmentStatuses()));
        }

        // Filter by transport order IDs
        if (!CollectionUtils.isEmpty(filter.getTransportOrderIds())) {
            spec = spec.and(transportOrderIdsIn(filter.getTransportOrderIds()));
        }

        // Filter by customer order IDs
        if (!CollectionUtils.isEmpty(filter.getCustomerOrderIds())) {
            spec = spec.and(customerOrderIdsIn(filter.getCustomerOrderIds()));
        }

        // Filter by vendor IDs
        if (!CollectionUtils.isEmpty(filter.getVendorIds())) {
            spec = spec.and(vendorIdsIn(filter.getVendorIds()));
        }

        // Filter by vehicle types
        if (!CollectionUtils.isEmpty(filter.getVehicleTypes())) {
            spec = spec.and(vehicleTypesIn(filter.getVehicleTypes()));
        }

        // Filter by vehicle registration numbers
        if (!CollectionUtils.isEmpty(filter.getVehicleRegistrationNumbers())) {
            spec = spec.and(vehicleRegistrationNumbersIn(filter.getVehicleRegistrationNumbers()));
        }

        // Filter by trailer IDs
        if (!CollectionUtils.isEmpty(filter.getTrailerIds())) {
            spec = spec.and(trailerIdsIn(filter.getTrailerIds()));
        }

        // Filter by vehicle operator IDs
        if (!CollectionUtils.isEmpty(filter.getVehicleOperatorIds())) {
            spec = spec.and(vehicleOperatorIdsIn(filter.getVehicleOperatorIds()));
        }

        // Filter by origin location ID
        if (StringUtils.hasText(filter.getOriginLocationId())) {
            spec = spec.and(originLocationIdEquals(filter.getOriginLocationId()));
        }

        // Filter by destination location ID
        if (StringUtils.hasText(filter.getDestinationLocationId())) {
            spec = spec.and(destinationLocationIdEquals(filter.getDestinationLocationId()));
        }

        // Filter by expected pickup date range
        if (filter.getExpectedPickupDateRange() != null) {
            spec = spec.and(dateInRange(ShipmentFieldConstants.EXPECTED_PICKUP_AT, filter.getExpectedPickupDateRange()));
        }

        // Filter by expected delivery date range
        if (filter.getExpectedDeliveryDateRange() != null) {
            spec = spec.and(dateInRange(ShipmentFieldConstants.EXPECTED_DELIVERY_AT, filter.getExpectedDeliveryDateRange()));
        }

        // Filter by actual pickup date range
        if (filter.getActualPickupDateRange() != null) {
            spec = spec.and(dateInRange(ShipmentFieldConstants.ACTUAL_PICKUP_AT, filter.getActualPickupDateRange()));
        }

        // Filter by actual delivery date range
        if (filter.getActualDeliveryDateRange() != null) {
            spec = spec.and(dateInRange(ShipmentFieldConstants.ACTUAL_DELIVERY_AT, filter.getActualDeliveryDateRange()));
        }

        // Filter by POD attachment status
        if (filter.getIsPodAttached() != null) {
            spec = spec.and(isPodAttachedFilter(filter.getIsPodAttached()));
        }

        return spec;
    }

    /**
     * Creates Pageable object with proper validation and defaults
     */
    private Pageable createPageable(Pagination pagination,
                                    Sort sortRequest) {
        // Handle pagination
        int pageNo = DEFAULT_PAGE_NO;
        int pageSize = DEFAULT_PAGE_SIZE;

        if (pagination != null) {
            pageNo = Math.max(0, pagination.getPageNo());
            pageSize = pagination.getPageSize() > 0 ?
                    Math.min(pagination.getPageSize(), MAX_PAGE_SIZE) : DEFAULT_PAGE_SIZE;
        }

        // Handle sorting
        org.springframework.data.domain.Sort sort = createSort(sortRequest);

        return PageRequest.of(pageNo, pageSize, sort);
    }

    /**
     * Creates Sort object with validation and defaults
     */
    private org.springframework.data.domain.Sort createSort(Sort sortRequest) {
        String sortBy = DEFAULT_SORT_BY;
        Direction direction = Direction.fromString(DEFAULT_SORT_ORDER);

        if (sortRequest != null) {
            if (StringUtils.hasText(sortRequest.getSortBy())) {
                sortBy = validateAndMapSortField(sortRequest.getSortBy());
            }

            if (StringUtils.hasText(sortRequest.getSortOrder())) {
                try {
                    direction = Direction.fromString(sortRequest.getSortOrder());
                } catch (IllegalArgumentException e) {
                    log.info("Invalid sort order: {}, using default DESC", sortRequest.getSortOrder());
                }
            }
        }

        return org.springframework.data.domain.Sort.by(direction, sortBy);
    }

    /**
     * Validates and maps sort field names to actual entity fields
     * Supports comprehensive sorting options for Shipment entities
     */
    private String validateAndMapSortField(String sortBy) {
        Map<String, String> fieldMapping = Map.ofEntries(
                // Basic Shipment fields
                Map.entry(ShipmentFieldConstants.SortFields.API_CODE, ShipmentFieldConstants.CODE),
                Map.entry(ShipmentFieldConstants.SortFields.API_STATUS, ShipmentFieldConstants.STATUS),
                Map.entry(ShipmentFieldConstants.SortFields.API_CREATED_AT, ShipmentFieldConstants.CREATED_AT),
                Map.entry(ShipmentFieldConstants.SortFields.API_UPDATED_AT, ShipmentFieldConstants.UPDATED_AT),

                // Related entity fields
                Map.entry(ShipmentFieldConstants.SortFields.API_TRIP_CODE, ShipmentFieldConstants.TRIP_CODE),
                Map.entry(ShipmentFieldConstants.SortFields.API_TRANSPORT_ORDER_CODE, ShipmentFieldConstants.TRANSPORT_ORDER_CODE),
                Map.entry(ShipmentFieldConstants.SortFields.API_CUSTOMER_ORDER_ID, ShipmentFieldConstants.EXTERNAL_CUSTOMER_ORDER_ID),
                Map.entry(ShipmentFieldConstants.SortFields.API_CONSIGNMENT_ID, ShipmentFieldConstants.EXTERNAL_CONSIGNMENT_ID),

                // Timestamp fields
                Map.entry(ShipmentFieldConstants.SortFields.API_EXPECTED_PICKUP_AT, ShipmentFieldConstants.EXPECTED_PICKUP_AT),
                Map.entry(ShipmentFieldConstants.SortFields.API_EXPECTED_DELIVERY_AT, ShipmentFieldConstants.EXPECTED_DELIVERY_AT),
                Map.entry(ShipmentFieldConstants.SortFields.API_ACTUAL_PICKUP_AT, ShipmentFieldConstants.ACTUAL_PICKUP_AT),
                Map.entry(ShipmentFieldConstants.SortFields.API_ACTUAL_DELIVERY_AT, ShipmentFieldConstants.ACTUAL_DELIVERY_AT),

                // Measurement fields
                Map.entry(ShipmentFieldConstants.SortFields.API_VOLUME, ShipmentFieldConstants.VOLUME),
                Map.entry(ShipmentFieldConstants.SortFields.API_WEIGHT, ShipmentFieldConstants.WEIGHT)
        );

        String mappedField = fieldMapping.get(sortBy);
        if (mappedField != null) {
            log.info("Mapped sort field '{}' to entity field '{}'", sortBy, mappedField);
            return mappedField;
        } else {
            log.info("Invalid sort field '{}', using default '{}'", sortBy, DEFAULT_SORT_BY);
            return DEFAULT_SORT_BY;
        }
    }

    // ===== SPECIFICATION METHODS =====

    private Specification<Shipment> tripIdsIn(List<String> tripIds) {
        return (root, query, builder) -> {
            List<String> validTripIds = tripIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid trip id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTripIds.isEmpty()) {
                log.info("No valid trip ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            return tripJoin.get(ShipmentFieldConstants.CODE).in(validTripIds);
        };
    }

    private Specification<Shipment> shipmentIdsIn(List<String> shipmentIds) {
        return (root, query, builder) -> {
            List<String> validShipmentIds = shipmentIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid shipment id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validShipmentIds.isEmpty()) {
                log.info("No valid shipment ids found");
                return builder.disjunction();
            }

            return root.get(ShipmentFieldConstants.CODE).in(validShipmentIds);
        };
    }

    private Specification<Shipment> tripStatusesIn(List<String> tripStatuses) {
        return (root, query, builder) -> {
            List<TripStatus> statuses = tripStatuses.stream()
                    .map(status -> {
                        try {
                            return TripStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid trip status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (statuses.isEmpty()) {
                log.info("No valid trip statuses found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            return tripJoin.get(ShipmentFieldConstants.STATUS).in(statuses);
        };
    }

    private Specification<Shipment> transportOrderStatusesIn(List<String> transportOrderStatuses) {
        return (root, query, builder) -> {
            List<TransportOrderStatus> statuses = transportOrderStatuses.stream()
                    .map(status -> {
                        try {
                            return TransportOrderStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid transport order status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (statuses.isEmpty()) {
                log.info("No valid transport order statuses found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return transportOrderJoin.get(ShipmentFieldConstants.STATUS).in(statuses);
        };
    }

    private Specification<Shipment> shipmentStatusesIn(List<String> shipmentStatuses) {
        return (root, query, builder) -> {
            List<ShipmentStatus> statuses = shipmentStatuses.stream()
                    .map(status -> {
                        try {
                            return ShipmentStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid shipment status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return statuses.isEmpty() ?
                    builder.disjunction() : root.get(ShipmentFieldConstants.STATUS).in(statuses);
        };
    }

    private Specification<Shipment> transportOrderIdsIn(List<String> transportOrderIds) {
        return (root, query, builder) -> {
            List<String> validTransportOrderIds = transportOrderIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid transport order id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTransportOrderIds.isEmpty()) {
                log.info("No valid transport order ids found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return transportOrderJoin.get(ShipmentFieldConstants.CODE).in(validTransportOrderIds);
        };
    }



    private Specification<Shipment> consignmentIdsIn(List<String> consignmentIds) {
        return (root, query, builder) -> {
            List<String> validConsignmentIds = consignmentIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid consignment id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validConsignmentIds.isEmpty()) {
                log.info("No valid consignment ids found");
                return builder.disjunction();
            }

            return root.get(ShipmentFieldConstants.EXTERNAL_CONSIGNMENT_ID).in(validConsignmentIds);
        };
    }



    private Specification<Shipment> customerOrderIdsIn(List<String> customerOrderIds) {
        return (root, query, builder) -> {
            List<String> validCustomerOrderIds = customerOrderIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid customer order id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validCustomerOrderIds.isEmpty()) {
                log.info("No valid customer order ids found");
                return builder.disjunction();
            }

            return root.get(ShipmentFieldConstants.EXTERNAL_CUSTOMER_ORDER_ID).in(validCustomerOrderIds);
        };
    }

    private Specification<Shipment> vendorIdsIn(List<String> vendorIds) {
        return (root, query, builder) -> {
            List<String> validVendorIds = vendorIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid vendor id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validVendorIds.isEmpty()) {
                log.info("No valid vendor ids found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return transportOrderJoin.get("assigneeIdentifier").in(validVendorIds);
        };
    }

    private Specification<Shipment> vehicleTypesIn(List<String> vehicleTypes) {
        return (root, query, builder) -> {
            List<String> validVehicleTypes = vehicleTypes.stream()
                    .filter(type -> {
                        if (!StringUtils.hasText(type)) {
                            log.info("Invalid vehicle type: {}", type);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validVehicleTypes.isEmpty()) {
                log.info("No valid vehicle types found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleResource> vehicleJoin = tripJoin.join("vehicleResource", JoinType.LEFT);
            return vehicleJoin.get("externalVehicleTypeId").in(validVehicleTypes);
        };
    }

    private Specification<Shipment> vehicleRegistrationNumbersIn(List<String> vehicleRegistrationNumbers) {
        return (root, query, builder) -> {
            List<String> validRegistrationNumbers = vehicleRegistrationNumbers.stream()
                    .filter(number -> {
                        if (!StringUtils.hasText(number)) {
                            log.info("Invalid vehicle registration number: {}", number);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validRegistrationNumbers.isEmpty()) {
                log.info("No valid vehicle registration numbers found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleResource> vehicleJoin = tripJoin.join("vehicleResource", JoinType.LEFT);
            return vehicleJoin.get("registrationNumber").in(validRegistrationNumbers);
        };
    }

    private Specification<Shipment> trailerIdsIn(List<String> trailerIds) {
        return (root, query, builder) -> {
            List<String> validTrailerIds = trailerIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid trailer id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTrailerIds.isEmpty()) {
                log.info("No valid trailer ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, TrailerResource> trailerJoin = tripJoin.join("trailerResources", JoinType.INNER);
            return trailerJoin.get("externalResourceId").in(validTrailerIds);
        };
    }

    private Specification<Shipment> vehicleOperatorIdsIn(List<String> vehicleOperatorIds) {
        return (root, query, builder) -> {
            List<String> validOperatorIds = vehicleOperatorIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid vehicle operator id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validOperatorIds.isEmpty()) {
                log.info("No valid vehicle operator ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleOperatorResource> operatorJoin = tripJoin.join("vehicleOperatorResources", JoinType.INNER);
            return operatorJoin.get("externalResourceId").in(validOperatorIds);
        };
    }

    private Specification<Shipment> originLocationIdEquals(String originLocationId) {
        return (root, query, builder) -> {
            Join<Shipment, Stop> originStopJoin = root.join(ShipmentFieldConstants.ORIGIN_STOP, JoinType.INNER);
            return builder.equal(originStopJoin.get(ShipmentFieldConstants.EXTERNAL_LOCATION_CODE), originLocationId);
        };
    }

    private Specification<Shipment> destinationLocationIdEquals(String destinationLocationId) {
        return (root, query, builder) -> {
            Join<Shipment, Stop> destinationStopJoin = root.join(ShipmentFieldConstants.DESTINATION_STOP, JoinType.INNER);
            return builder.equal(destinationStopJoin.get(ShipmentFieldConstants.EXTERNAL_LOCATION_CODE), destinationLocationId);
        };
    }

    private Specification<Shipment> isPodAttachedFilter(Boolean isPodAttached) {
        return (root, query, builder) -> {
            // This would need to be implemented based on how POD attachment is tracked in the system
            // For now, returning a placeholder that always matches
            // TODO: Implement actual POD attachment logic based on business requirements
            return builder.conjunction();
        };
    }



    private Specification<Shipment> dateInRange(String fieldName, DateRange dateRange) {
        return (root, query, builder) -> {
            if (dateRange.getFrom() != null && dateRange.getTo() != null) {
                return builder.and(
                    builder.greaterThanOrEqualTo(root.get(fieldName), dateRange.getFrom()),
                    builder.lessThanOrEqualTo(root.get(fieldName), dateRange.getTo())
                );
            } else if (dateRange.getFrom() != null) {
                return builder.greaterThanOrEqualTo(root.get(fieldName), dateRange.getFrom());
            } else if (dateRange.getTo() != null) {
                return builder.lessThanOrEqualTo(root.get(fieldName), dateRange.getTo());
            }

            return builder.conjunction();
        };
    }
}
