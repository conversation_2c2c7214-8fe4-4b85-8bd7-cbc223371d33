package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.entity.Document;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * Maps {@link Document} ↔ {@link DocumentDTO}.
 */
@Mapper(componentModel = "spring")
public interface DocumentMapper {

    @Mappings({
            @Mapping(target = "tripCode",     source = "tripCode"),
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum",     source = "checksum"),
            @Mapping(target = "fileIdentifier",  source = "fileIdentifier")
    })
    DocumentDTO toDTO(Document document);

    @Mappings({
            @Mapping(target = "tripCode",       source = "tripCode"),
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum",     source = "checksum"),
            @Mapping(target = "fileIdentifier",  source = "fileIdentifier")
    })
    Document toEntity(DocumentDTO dto);


}
