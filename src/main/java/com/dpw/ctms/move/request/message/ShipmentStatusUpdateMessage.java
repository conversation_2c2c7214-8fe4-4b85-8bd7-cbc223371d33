package com.dpw.ctms.move.request.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentStatusUpdateMessage {
    private String shipmentCode;
    private String extShipmentCode;
    private String externalConsignmentId;
    private String previousStatus;
    private String currentStatus;
    private Long pickupTime;
    private Long deliveryTime;
    private String updatedBy;
    private Long updatedAt;
    private String comments;
    private String eventType;
}
