package com.dpw.ctms.move.request.common;

import com.dpw.tmsutils.threadlocal.UserContext;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class IntegratorMessageHeader {
    private String action;
    private String correlationId;
    private String sequenceId;
    private Long dateTime;
    private String site;
    private String userName;
    private String type;
    private String version;
    private String token;
    private String source;
    private String eventStatus;
    private String topic;
    private UserContext userContext;
}
