package com.dpw.ctms.move.request.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TripStatusUpdateMessage {
    private String tripCode;
    private String extTripCode;
    private String previousStatus;
    private String currentStatus;
    private Long startTime;
    private Long endTime;
    private String updatedBy;
    private Long updatedAt;
    private String comments;
    private String eventType;
}

