package com.dpw.ctms.move.enums;

public enum StopStatus implements DisplayableStatusEnum {
    PLANNED("Planned"),
    PARTIALLY_COMPLETED("Partially completed"),
    COMPLETED("Completed"),
    DISCARDED("Discarded");

    private final String displayText;

    StopStatus(String displayText) {
        this.displayText = displayText;
    }

    @Override
    public String getDisplayName() {
        return this.displayText;
    }
}