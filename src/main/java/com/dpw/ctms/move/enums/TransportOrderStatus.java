package com.dpw.ctms.move.enums;

public enum TransportOrderStatus implements DisplayableStatusEnum {

    ASSIGNED("Assigned"),
    IN_PROGRESS("In Progress"),
    EXECUTED("Executed"),
    EXECUTED_WITH_EXCEPTIONS("Executed with exceptions"),
    CANCELLED("Cancelled"),
    CLOSED("Closed");

    private String displayText;

    TransportOrderStatus(String displayText) {
        this.displayText = displayText;
    }

    @Override
    public String getDisplayName() {
        return this.displayText;
    }

    public static TransportOrderStatus fromDisplayName(String displayText) {
        if (displayText == null) {
            return null;
        }
        for (TransportOrderStatus status : TransportOrderStatus.values()) {
            if (status.displayText.equalsIgnoreCase(displayText)) {
                return status;
            }
        }
        return null;
    }
}
