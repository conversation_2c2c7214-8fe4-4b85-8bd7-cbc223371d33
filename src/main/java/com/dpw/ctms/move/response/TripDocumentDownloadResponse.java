package com.dpw.ctms.move.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TripDocumentDownloadResponse {
    String documentId;
    String presignedDownloadUrl;
}
