package com.dpw.ctms.move.config;

import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ConfigService {

    public JsonNode getConfig(String key, Vendor vendor) {
        Map<String, JsonNode> configMap = new HashMap<>();

        configMap.put(ConfigConstants.BOL_CONFIG, JsonUtils.toJsonNodeFromString("{\"templateId\":\"d7538162f4031a0a\",\"joltConfig\":[{ \"operation\": \"shift\", \"spec\": { \"shipmentIds\": { \"0\": \"shipmentNumber\" }, \"tripDetails\": { \"actualStartAt\": { \"iso\": \"actualDeparture\" }, \"actualEndAt\": { \"iso\": \"actualArrival\" }, \"expectedEndAt\": { \"iso\": \"eta\" }, \"externalOriginLocationCode\": \"originCode\", \"externalDestinationLocationCode\": \"destCode\" }, \"consignmentDetailsMap\": { \"*\": { \"specialInstructions\": \"goodsDescription\", \"customerOrderMetaData\": { \"internalReferenceNumber\": \"purchaseOrderNo\", \"customerOrderNumber\": \"orderNo\" }, \"expectedPickupTime\": { \"iso\": \"etd\" }, \"productDetailsDTO\": { \"properties\": { \"WEIGHT\": { \"value\": \"packagingAndDimension.weight\", \"resourceUomName\": \"packagingAndDimension.weightUom\" }, \"VOLUME\": { \"value\": \"packagingAndDimension.volume\", \"resourceUomName\": \"packagingAndDimension.volumeUom\" } } } } }, \"facilityDetailsMap\": \"facilityMap\" } }, { \"operation\": \"shift\", \"spec\": { \"shipmentNumber\": \"shipmentNumber\", \"actualDeparture\": \"actualDeparture\", \"actualArrival\": \"actualArrival\", \"orderNo\": \"orderNo\", \"purchaseOrderNo\": \"purchaseOrderNo\", \"goodsDescription\": \"goodsDescription\", \"etd\": \"etd\", \"eta\": \"eta\", \"packagingAndDimension\": \"packagingAndDimension\", \"originCode\": { \"*\": { \"@(2,facilityMap.&.name)\": \"shipFrom.name\", \"@(2,facilityMap.&.addressDetails.addressLine)\": \"shipFrom.addressLine\", \"@(2,facilityMap.&.pointOfContactDetails.email)\": \"shipFrom.emailId\" } }, \"destCode\": { \"*\": { \"@(2,facilityMap.&.name)\": \"shipTo.name\", \"@(2,facilityMap.&.addressDetails.addressLine)\": \"shipTo.addressLine\", \"@(2,facilityMap.&.pointOfContactDetails.email)\": \"shipTo.emailId\" } } } }, { \"operation\": \"default\", \"spec\": { \"shipmentNumber\": \"N/A\", \"actualDeparture\": \"N/A\", \"actualArrival\": \"N/A\", \"orderNo\": \"N/A\", \"purchaseOrderNo\": \"N/A\", \"goodsDescription\": \"N/A\", \"etd\": \"N/A\", \"eta\": \"N/A\", \"packagingAndDimension\": { \"weight\": \"N/A\", \"weightUom\": \"N/A\", \"volume\": \"N/A\", \"volumeUom\": \"N/A\" }, \"shipFrom\": { \"name\": \"N/A\", \"addressLine\": \"N/A\", \"emailId\": \"N/A\" }, \"shipTo\": { \"name\": \"N/A\", \"addressLine\": \"N/A\", \"emailId\": \"N/A\" } } }, { \"operation\": \"remove\", \"spec\": { \"originCode\": \"\", \"destCode\": \"\", \"facilityMap\": \"\" }}]}"));
        return configMap.get(key);
    }

}
