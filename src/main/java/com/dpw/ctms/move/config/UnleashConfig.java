package com.dpw.ctms.move.config;

import com.dpw.ctms.move.constants.UnleashConstant;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.TMSConfigUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
@Slf4j
public class UnleashConfig {

    public JsonNode getMoveConfig(String featureName) {
        String tenantId = TenantContext.getCurrentTenant();
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode contextNode = objectMapper.createObjectNode();
        contextNode.put(UnleashConstant.TENANT, tenantId);
        JsonNode configuration = null;

        try {
            configuration = TMSConfigUtils.getFlagValue(featureName, contextNode, JsonNode.class);
        } catch (Exception e) {
            log.error("Error fetching feature flag: {}", e.getMessage());
        }

        return configuration;
    }

}
