CREATE TABLE IF NOT EXISTS document (
    id              BIGSERIAL PRIMARY KEY,
    trip_code       VA<PERSON><PERSON>R NOT NULL,
    document_type   VARCHAR NOT NULL,
    download_url    VARCHAR,
    checksum        VARCHAR,
    file_identifier VARCHAR,
    created_at      BIGINT NOT NULL,
    updated_at      BIGINT,
    created_by      <PERSON><PERSON><PERSON>R NOT NULL,
    updated_by      VARCHAR
);

CREATE TABLE IF NOT EXISTS document_aud (
    id              BIGSERIAL,
    rev             BIGINT NOT NULL,
    revtype         SMALLINT,
    trip_code       VA<PERSON>HAR,
    document_type   VARCHAR,
    download_url    VARCHAR,
    checksum        VARCHAR,
    file_identifier VARCHAR,
    created_at      BIGINT,
    updated_at      BIGINT,
    created_by      <PERSON><PERSON><PERSON><PERSON>,
    updated_by      VA<PERSON><PERSON><PERSON>,
    CONSTRAINT document_aud_pkey PRIMARY KEY (id, rev),
    CONSTRAINT document_aud_revinfo FOREIGN KEY (rev) REFERENCES revinfo (rev)
);

CREATE INDEX IF NOT EXISTS idx_document_trip_code_document_type_checksum ON document (trip_code, document_type, checksum);
CREATE INDEX IF NOT EXISTS idx_document_trip_code ON document (trip_code);
